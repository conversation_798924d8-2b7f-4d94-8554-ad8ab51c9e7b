#%%

import pyrebase

from config import *


firebase = pyrebase.initialize_app(FIREBASE_CONFIG)

db = firebase.database()
auth = firebase.auth()

#%%
# Push Data
""" data = {
    'Department': 'ORT',
    'Ward': '3D',
    'Last Name': 'LAM',
    'First Name': '<PERSON>u Ming',
    'CORP ID': '468437'
}

 """


# permission = admin_permission + unit_permission


# db.child('permission').set(permission)


# Update Data

# db.child(data['CORP ID']).update({'Ward':'2A','Department':'MED'})


# Delete
# db.child(data['CORP ID']).remove()

#%%

# Get Data

# db_permission = db.get('permission')

# %%

""" for unit in unit_permission:
    auth.create_user_with_email_and_password(
        email=unit['unit'],
        password=unit['unit'].split('.')[0]
    ) """

# %%

# tests = dict(db.child('test').get().val())

# for test_name, test_versions in tests.items():
#     if test_name.startswith('PCA'):
#         for version_name, test_version in test_versions.items():
#             if test_version['info'].get('form_type','') == 'checklist':
#                 if 'basic_entry' in test_version['content']:
#                     # print(f"header: {test_version['content']['additional_entry']['header']}")
#                     # if '評分結果' in test_version['content']['additional_entry']['header']:
#                     #     print(f'Handling {test_name} {version_name}...')
#                     #     tests[test_name][version_name]['content']['additional_entry']['header'] = '評語'

#                     # tests[test_name][version_name]['content']['basic_entry']['details'] = {
#                     #     k:v
#                     #     for k,v in test_version['content']['additional_entry']['details'].items()
#                     #     if '總評分等級' not in v['title']
#                     # }

#                     print(f"Handling {test_name} {version_name}...")
#                     for k,v in test_version['content']['basic_entry']['details'].items():
#                         if k == 'basic_entry_02' and v['title'] == '職級':
#                             print(f'Changing to 指導員職級...')
#                             tests[test_name][version_name]['content']['basic_entry']['details'][k]['title'] = '指導員職級'
#                         elif k == 'basic_entry_05' and v['title'] == '職級':
#                             print(f'Changing to 觀察員職級...')
#                             tests[test_name][version_name]['content']['basic_entry']['details'][k]['title'] = '觀察員職級'
        


# db.child('test').set(tests)
                
# %%

def change_dept_of_unit(db, unit, old_dept, new_dept):

    permission = dict(db.child('permission').get().val())
    for key, data in permission.items():
        if key == unit:
            permission[key]['dept'] = new_dept
            permission[key]['access'] = [new_dept]

    db.child('permission').set(permission)
    print(f'Changed department of {unit} from {old_dept} to {new_dept}')

    for discipline in ['nurse', 'pca']:
        staff = dict(db.child(discipline).get().val())
        found = False
        for employee_id, data in staff.items():
            if data['Unit'] == unit:
                staff[employee_id]['Department'] = new_dept
                found = True
        
        if found:
            db.child(discipline).set(staff)
            print(f'Changed {discipline} of {unit} from {old_dept} to {new_dept}')
# %%

def merge_units(db, unit_a, unit_b):
    '''
    unit_b will be merged into unit_a
    '''

    permission = dict(db.child('permission').get().val())
    found_permission = False
    for key, data in permission.items():
        if key == unit_b:
            found_permission = True
            break

    if found_permission:
        removed_permission = permission.pop(unit_b)
        db.child('permission').set(permission)
        print(f'Removed {unit_b} from permission')

    else:
        print(f'Unit {unit_b} not found in permission')

    for discipline in ['nurse', 'pca']:
        staff = dict(db.child(discipline).get().val())
        found = False
        for employee_id, data in staff.items():
            if data['Unit'] == unit_b:
                staff[employee_id]['Unit'] = unit_a
                staff[employee_id]['Department'] = permission[unit_a]['dept']
                found = True
        
        if found:
            db.child(discipline).set(staff)
            print(f'Merged {unit_b} into {unit_a}')

        else:
            print(f'Unit {unit_b} not found in {discipline}')

#%%

def change_unit_name(db, old_name, new_name):

    permission = dict(db.child('permission').get().val())

    found_permission = False
    for key, data in permission.items():
        if key == old_name:
            found_permission = True
            new_permission = data
            new_permission['unit'] = new_name
            permission[new_name] = new_permission
            break
    
    if found_permission:
        removed_permission = permission.pop(old_name)
        db.child('permission').set(permission)
        print(f'Changed unit name from {old_name} to {new_name}')

    else:
        print(f'Unit {old_name} not found in permission')

    for discipline in ['nurse', 'pca']:
        staff = dict(db.child(discipline).get().val())
        found = False
        for employee_id, data in staff.items():
            if data['Unit'] == old_name:
                found = True
                staff[employee_id]['Unit'] = new_name

        if found:
            db.child(discipline).set(staff)
            print(f'Changed unit name of {discipline} from {old_name} to {new_name}')

        else:
            print(f'Unit {old_name} not found in {discipline}')
# %%

def change_staff_unit(db, employee_id, new_unit):

    staff_discipline = ''
    for discipline in ['nurse', 'pca', 'temp_staff']:
        staff = dict(db.child(discipline).get().val()) if db.child(discipline).get().val() else {}
        if staff:
            staff_discipline = discipline
            break

        
    if not staff: 
        print('No staff found')
        return

    updated = False
    for key, data in staff.items():
        if key == employee_id:
            permissions = dict(db.child('permission').get().val()) if db.child('permission').get().val() else {}
            if permissions:
                dept = permissions.get(new_unit, {}).get('dept', '')
                if dept:
                    staff[key]['Department'] = dept
                    staff[key]['Unit'] = new_unit
                    updated = True
                else:
                    print(f'No department found for {new_unit}')
                    return
            break
                
    if updated:
        db.child(staff_discipline).set(staff)
        print(f'Changed unit of {employee_id} from {staff_discipline} to {new_unit}')
# %%

# %%
def change_test_name(db, old_test_name, new_test_name):
    old_tests = dict(db.child('test').child(old_test_name).get().val())
    new_tests = {}
    for v_id, version in old_tests.items():
        temp_test = version
        temp_test['info']['test_name'] = new_test_name
        new_tests[v_id] = temp_test

    db.child('test').child(new_test_name).set(new_tests)
    db.child('test').child(old_test_name).remove()

#%%

# tests = dict(db.child('test').get().val())
# for test_name, data in tests.items():
#     for v_id, version in data.items():
#         if version['info']['form_type'] == 'checklist':
#             for o, option in version['content']['questions']['details']['options'].items():
#                 if 'count_score' not in option:
#                     print(f'Adding count_score for {test_name} {v_id} {o}')
#                     tests[test_name][v_id]['content']['questions']['details']['options'][o]['count_score'] = False
#                     if option['name'] == '滿意':
#                         tests[test_name][v_id]['content']['questions']['details']['options'][o]['count_score'] = True

# db.child('test').set(tests)
    
# %%

# tests = dict(db.child('test').get().val())
# for test_name, data in tests.items():
#     print(f'Handling {test_name}')
#     for v_id, version in data.items():
#         if version['info']['form_type'] == 'checklist':
#             for entry_type in['basic_entry', 'additional_entry']:
#                 if entry_type in version['content']:
#                     for b_k, b_v in version['content'][entry_type]['details'].items():
#                         if b_v['title'] == '指導員姓名' or b_v['title'] == '觀察員姓名':
#                             try:
#                                 next_b_k = list(version['content'][entry_type]['details'].keys())[list(version['content'][entry_type]['details'].values()).index(b_k)+1]
#                             except:
#                                 next_b_k = None
#                             if next_b_k == '職級':
#                                 print(f'Changing {test_name} {v_id} {entry_type} {b_k} {b_v["title"]} title')
#                                 tests[test_name][v_id]['content'][entry_type]['details'][next_b_k]['title'] = f'{b_v["title"].strip().replace("姓名", "")}職級'
                                
# db.child('test').set(tests)
# %%

def migrate_assessment_structure_to_date_based(db):
    """
    Migrate all staff assessment results from old structure to new date-based structure.

    Old structure: Assessments -> test_name -> version -> {result content with 'time' field}
    New structure: Assessments -> test_name -> version -> date -> {result content with 'time' field}

    This function scans all staff in 'nurse', 'pca', and 'temp_staff' collections.
    """

    print("Starting migration of assessment structure to date-based format...")

    # Track migration statistics
    total_staff = 0
    migrated_staff = 0
    total_assessments = 0
    migrated_assessments = 0

    # Process each staff collection
    for collection_name in ['nurse', 'pca', 'temp_staff']:
        print(f"\nProcessing {collection_name} collection...")

        # Get all staff data from this collection
        staff_data = db.child(collection_name).get().val()
        if not staff_data:
            print(f"No data found in {collection_name} collection")
            continue

        collection_staff_count = 0
        collection_migrated_count = 0

        # Process each staff member
        for staff_id, staff_info in staff_data.items():
            total_staff += 1
            collection_staff_count += 1

            # Check if staff has assessments
            if 'Assessments' not in staff_info:
                continue

            assessments = staff_info['Assessments']
            staff_needs_migration = False
            migrated_staff_assessments = {}

            # Process each test
            for test_name, test_versions in assessments.items():
                migrated_test_versions = {}

                # Process each version
                for version_name, version_data in test_versions.items():
                    total_assessments += 1

                    # Check if this version needs migration
                    # New structure has date keys (YYYY-MM-DD format)
                    # Old structure has direct result content
                    if isinstance(version_data, dict):
                        # Check if any key looks like a date (contains '-' and is 10 chars)
                        has_date_keys = any(
                            isinstance(key, str) and '-' in key and len(key) == 10
                            for key in version_data.keys()
                        )

                        if has_date_keys:
                            # Already in new format, keep as is
                            migrated_test_versions[version_name] = version_data
                        else:
                            # Old format, needs migration
                            staff_needs_migration = True
                            migrated_assessments += 1

                            # Extract the test date from the 'time' field
                            test_date = version_data.get('time', '')

                            # Handle different time formats
                            if test_date:
                                # Extract date part (YYYY-MM-DD)
                                if ' ' in test_date:
                                    # Format: "2024-01-29 14:13"
                                    date_part = test_date.split(' ')[0]
                                else:
                                    # Format: "2025-06-10"
                                    date_part = test_date

                                # Validate date format
                                if len(date_part) == 10 and date_part.count('-') == 2:
                                    test_date_key = date_part
                                else:
                                    # Fallback to current date if invalid
                                    from datetime import datetime
                                    import pytz
                                    hong_kong_tz = pytz.timezone('Asia/Hong_Kong')
                                    test_date_key = datetime.now(hong_kong_tz).strftime("%Y-%m-%d")
                                    print(f"Warning: Invalid date format '{test_date}' for {staff_id}, using current date")
                            else:
                                # No time field, use current date
                                from datetime import datetime
                                import pytz
                                hong_kong_tz = pytz.timezone('Asia/Hong_Kong')
                                test_date_key = datetime.now(hong_kong_tz).strftime("%Y-%m-%d")
                                print(f"Warning: No time field for {staff_id} {test_name} {version_name}, using current date")

                            # Create new structure with date as key
                            migrated_test_versions[version_name] = {
                                test_date_key: version_data
                            }
                    else:
                        # Unexpected data type, keep as is
                        migrated_test_versions[version_name] = version_data

                migrated_staff_assessments[test_name] = migrated_test_versions

            # Update staff data if migration was needed
            if staff_needs_migration:
                migrated_staff += 1
                collection_migrated_count += 1

                # Update the staff record with migrated assessments
                staff_data[staff_id]['Assessments'] = migrated_staff_assessments

                print(f"Migrated assessments for {collection_name} staff ID: {staff_id}")

        # Update the entire collection if any staff were migrated
        if collection_migrated_count > 0:
            print(f"Updating {collection_name} collection with {collection_migrated_count} migrated staff records...")
            db.child(collection_name).set(staff_data)
            print(f"Successfully updated {collection_name} collection")

        print(f"{collection_name} summary: {collection_migrated_count}/{collection_staff_count} staff migrated")

    # Print final migration summary
    print(f"\n=== Migration Summary ===")
    print(f"Total staff processed: {total_staff}")
    print(f"Staff with migrated assessments: {migrated_staff}")
    print(f"Total assessment records processed: {total_assessments}")
    print(f"Assessment records migrated: {migrated_assessments}")
    print(f"Migration completed successfully!")

    return {
        'total_staff': total_staff,
        'migrated_staff': migrated_staff,
        'total_assessments': total_assessments,
        'migrated_assessments': migrated_assessments
    }

# %%

def change_test_key(db, old_test_key, new_test_key):
    """
    Change the key of a test inside the root "test" key.
    This function renames a test by moving all its versions to a new key
    and updating the test_name in the info section of each version.

    Args:
        db: Firebase database instance
        old_test_key (str): Current test key name
        new_test_key (str): New test key name

    Returns:
        bool: True if successful, False if failed
    """

    try:
        # Get the existing test data
        existing_test_data = db.child('test').child(old_test_key).get().val()

        if not existing_test_data:
            print(f'Error: Test with key "{old_test_key}" not found')
            return False

        # Check if new test key already exists
        existing_new_test = db.child('test').child(new_test_key).get().val()
        if existing_new_test:
            print(f'Error: Test with key "{new_test_key}" already exists')
            return False

        # Create new test data with updated test_name in info sections
        new_test_data = {}
        versions_updated = 0

        for version_key, version_data in existing_test_data.items():
            # Copy the version data
            updated_version = dict(version_data)

            # Update the test_name in the info section if it exists
            if 'info' in updated_version and 'test_name' in updated_version['info']:
                updated_version['info']['test_name'] = new_test_key
                versions_updated += 1

            new_test_data[version_key] = updated_version

        # Set the new test data
        db.child('test').child(new_test_key).set(new_test_data)

        # Remove the old test data
        db.child('test').child(old_test_key).remove()

        print(f'Successfully changed test key from "{old_test_key}" to "{new_test_key}"')
        print(f'Updated {versions_updated} version(s) with new test_name')

        return True

    except Exception as e:
        print(f'Error changing test key: {str(e)}')
        return False

# %%

# Example usage of the migration function:
# Uncomment the following lines to run the migration

# print("Running assessment structure migration...")
# migration_result = migrate_assessment_structure_to_date_based(db)
# print(f"Migration completed: {migration_result}")

# %%
