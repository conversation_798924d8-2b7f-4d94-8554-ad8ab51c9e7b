<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{{ basic_app_config['name'] }}</title>
    <link
      rel="icon"
      href="{{url_for('static',filename='favicon.ico')}}"
      type="image/x-icon"
    />
    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha2/dist/css/bootstrap.min.css"
      rel="stylesheet"
      integrity="sha384-aFq/bzH65dt+w6FI2ooMVUpc+21e0SRygnTpmBvdBgSdnuTN7QbdgL+OapgHtvPp"
      crossorigin="anonymous"
    />
    <!-- JQuery -->
    <script
      src="https://code.jquery.com/jquery-3.7.0.min.js"
      integrity="sha256-2Pmvv0kuTBOenSvLm6bvfBSSHrUJ+3A7x6P5Ebd07/g="
      crossorigin="anonymous"
    ></script>
    <!-- Font Awesome ---->
    <script src="https://kit.fontawesome.com/7e388db5d6.js" crossorigin="anonymous"></script>
    <!-- <script src="https://use.fontawesome.com/ce99800404.js"></script> -->
    <!-- Popperjs -->
    <script
      src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"
      crossorigin="anonymous"
    ></script>
    <!-- Tempus Dominus JavaScript -->
    <script
      src="https://cdn.jsdelivr.net/npm/@eonasdan/tempus-dominus@6.7.7/dist/js/tempus-dominus.min.js"
      crossorigin="anonymous"
    ></script>
    <!-- Tempus Dominus Styles -->
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/@eonasdan/tempus-dominus@6.7.7/dist/css/tempus-dominus.min.css"
      crossorigin="anonymous"
    />
    <!-- jQuery UI -->
    <link
      rel="stylesheet"
      href="https://code.jquery.com/ui/1.13.0/themes/base/jquery-ui.css"
    />
    <script src="https://code.jquery.com/ui/1.13.0/jquery-ui.min.js"></script>

    {% block custom_head %}{% endblock %}
    <!-- Optional: Add custom CSS styles here -->

    <style>
      .navbar-nav .nav-link {
        margin-right: 1rem;
        border-radius: 15px;
      }

      .navbar-nav .nav-link:hover {
        background-color: #72c289;
        color: #013420;
      }

      .navbar-nav .nav-link:active {
        background-color: #010150;
        color: #ffffff;
      }
    </style>
  </head>
  <body>
    {% block bodyTop %}
    {% endblock %} 

    {% with messages = get_flashed_messages(with_categories=true) %} 
    {% if messages %}
    <div class="container">
      {% for category, message in messages %}
      <div
        class="alert alert-{{ category }} alert-dismissible fade show"
        role="alert"
        id="flash-message"
      >
        {{ message }}
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="alert"
          aria-label="Close"
        ></button>
        <span id="countdown" class="ms-3"></span>
      </div>
      {% endfor %}
    </div>
    <script>
      const countdownElement = document.getElementById("countdown");
      let countdown = 2;
      const countdownInterval = setInterval(() => {
        countdownElement.innerText = `(Closing in ${countdown} seconds)`;
        countdown -= 1;
        if (countdown < 0) {
          clearInterval(countdownInterval);
          const flashMessage = document.querySelector("#flash-message");
          flashMessage.remove();
        }
      }, 1000);
    </script>
    {% endif %} 
    {% endwith %}

    <nav class="navbar navbar-expand-lg navbar-dark bg-success">
      <div class="container">
        <a class="navbar-brand ml-auto" href="{{ url_for('index') }}"
          >{{ basic_app_config['name'] }}</a
        >
        <button
          class="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarNav"
          aria-controls="navbarNav"
          aria-expanded="false"
          aria-label="Toggle navigation"
        >
          <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
          <ul class="navbar-nav ms-auto mb-2 mb-lg-0">
            {% if 'user_id' in session %} {% if session['user_email'] ==
            basic_app_config['retriever'] %}
            <li class="nav-item">
              <a class="ps-2 nav-link" href="{{ url_for('logout') }}">Logout</a>
            </li>
            {% else %}
            <li class="nav-item">
              <a class="ps-2 nav-link" href="{{ url_for('ui') }}">Home</a>
            </li>
            {% if session['admin'] %}
            <li class="nav-item">
              <a class="ps-2 nav-link" href="{{ url_for('admin') }}">Admin</a>
            </li>
            {% endif %}
            <li class="nav-item">
              <a class="ps-2 nav-link" href="{{ url_for('logout') }}">Logout</a>
            </li>
            {% endif %} {% else %}
            <li class="nav-item">
              <a class="ps-2 nav-link" href="{{ url_for('login') }}">Login</a>
            </li>
            {% endif %}
          </ul>
        </div>
      </div>
    </nav>

    <main class="container-fluid p-1 mt-4 mx-1">{% block content %}{% endblock %}</main>

    <div
      class="modal fade"
      id="general-alert-modal"
      tabindex="-1"
      role="dialog"
      aria-labelledby="general-alert-modal-label"
    >
      <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
          <div class="modal-header" id="general-alert-modal-header">
            <h5 
              class="modal-title" 
              id="general-alert-modal-header-title"
            ></h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
            ></button>
          </div>
          <div class="modal-body">
            <p id="general-alert-modal-msg"></p>
          </div>
          <div class="modal-footer">
            <button 
              type="button" 
              class="btn btn-secondary" 
              data-bs-dismiss="modal" 
              id="general-alert-modal-footer-button"
            >Close</button>
          </div>
        </div>
      </div>
    </div>
    
    
    <!-- Bootstrap JavaScript and its dependencies -->
    <script
      src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha2/dist/js/bootstrap.bundle.min.js"
      integrity="sha384-qKXV1j0HvMUeCBQ+QVp7JcfGl760yU08IQ+GpUo5hlbpg51QRiuqHAJz8+BrxE/N"
      crossorigin="anonymous"
    ></script>
    <script>
      function flash(elementID, message, category, duration = 3000) {
        const flashElement = document.getElementById(elementID);
        const alertType = `alert-${category}`;
        flashElement.classList.add(alertType);
        flashElement.innerText = message;
        const closeButton = document.createElement("button");

        closeButton.type = "button";
        closeButton.className = "btn-close";
        closeButton.setAttribute("data-bs-dismiss", "alert");
        closeButton.setAttribute("aria-label", "Close");

        const countdownSpan = document.createElement("span");
        countdownSpan.id = "countdown";
        countdownSpan.className = "ms-3";

        flashElement.appendChild(closeButton);
        flashElement.appendChild(countdownSpan);

        flashElement.style.display = "block";

        const countdownElement = document.getElementById("countdown");
        let countdown = 2;
        const countdownInterval = setInterval(() => {
          countdownElement.innerText = `(Closing in ${countdown} seconds)`;
          countdown -= 1;
          if (countdown < 0) {
            clearInterval(countdownInterval);
            flashElement.style.display = "none";
          }
        }, 1000);
      }
    </script>
  </body>
</html>
