{% extends 'base.html' %} {% block custom_head %}
<link
  href="https://fonts.googleapis.com/css?family=Roboto:wght@800&display=swap"
  rel="stylesheet"
/>
<style>
  html,
  body {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
  }

  .bg-primary {
    background-color: #007bff !important;
  }

  tr.even {
    background-color: #f2f2f2;
  }

  .d-flex {
    display: flex;
  }

  .justify-content-between {
    justify-content: space-between;
  }

  .align-items-center {
    align-items: center;
  }

  .form-group {
    margin-bottom: 0;
  }



  .container {
    width: 100%;
    height: 100%;
  }

  .tab-content {
    width: 100%;
    height: 100%;
  }

  .tab-pane {
    width: 100%;
    height: 100%;
  }

  table.custom-table thead th {
    background-color: #0a8853;
    font-family: "Roboto", sans-serif;
    font-weight: 800;
    color: white;
  }

  .filter-section .card-header {
    background-color: #e9ecef;
    border-bottom: 1px solid #dee2e6;
  }

  .filter-section .btn-link {
    color: #495057;
    text-decoration: none;
  }

  .filter-section .btn-link:hover {
    color: #007bff;
    text-decoration: none;
  }

  .filter-section .form-control {
    font-size: 0.9rem;
  }

  .filter-section input[type="date"] {
    font-size: 0.9rem;
    padding: 0.375rem 0.75rem;
  }

  #filterStatus {
    font-size: 0.85rem;
  }

  /* Sortable table headers */
  .sortable {
    cursor: pointer;
    user-select: none;
    position: relative;
  }

  .sortable:hover {
    background-color: #0a7a4a !important;
  }

  .sort-icon {
    margin-left: 8px;
    opacity: 0.6;
    font-size: 0.8em;
  }

  .sortable[data-sort="asc"] .sort-icon:before {
    content: "\f0de"; /* fa-sort-up */
  }

  .sortable[data-sort="desc"] .sort-icon:before {
    content: "\f0dd"; /* fa-sort-down */
  }

  .sortable[data-sort="none"] .sort-icon:before {
    content: "\f0dc"; /* fa-sort */
  }

  .btn-link {
    text-decoration: none !important;
  }
</style>
{% endblock %} 

{% block content %}
<div class="container">
  <div class="d-flex justify-content-between align-items-center">
    <div>
    <h1>{{view_result}}</h1>
    {% if query_by == 'person' %}
    <h2><b style="color: blue;">{{ result_data[0].get('Name', 'NIL') }} ({{ result_data[0].get('Employee_No','NIL') }})</b></h2>
    {% endif %}
    </div>
    <div class="form-group">
      <button
        id="back-button"
        class="btn btn-secondary"
      >
        Back
      </button>
    </div>
  </div>
  {% if query_test != 'ALL'%}
    <h1><strong>{{query_test|replace('_', ' ')}}</strong></h1>
    <h2><strong>{{test_form.content.get('intro',{}).get('header','')}}</strong></h2>
    <div class="mb-3">
      <small class="text-muted mb-3">{{test_form.info.version_name}}</small>
    </div>
  {% endif %}
  <!--
  <div class="d-flex justify-content-between align-items-center">
    <h3>Department: {{ dept }}</h3>
    <div class="form-group">
      <button
        class="btn btn-success float-end mb-3"
        onclick="exportTableToExcel()"
      >
        Export
      </button>
    </div>
  </div>
  -->

  <!-- Filter Section -->
  <div class="card mb-3 filter-section">
    <div class="card-header">
      <h5 class="mb-0">
        <button class="btn btn-link" type="button" data-bs-toggle="collapse" data-bs-target="#filterCollapse" aria-expanded="true" aria-controls="filterCollapse">
          <i class="fas fa-filter"></i> Filters
        </button>
      </h5>
    </div>
    <div id="filterCollapse" class="collapse show">
      <div class="card-body">
        <div class="row">
          {% if query_test != 'ALL' %}
          <div class="col-md-3">
            <label for="unitFilter">Unit:</label>
            <select id="unitFilter" class="form-control">
              <option value="">All Units</option>
            </select>
          </div>
          <div class="col-md-3">
            <label for="nameFilter">Name:</label>
            <input type="text" id="nameFilter" class="form-control" placeholder="Search by name...">
          </div>
          <div class="col-md-3">
            <label for="employeeNoFilter">Employee No:</label>
            <input type="text" id="employeeNoFilter" class="form-control" placeholder="Search by employee no...">
          </div>
          {% else %}
          <div class="col-md-4">
            <label for="testFilter">Test:</label>
            <select id="testFilter" class="form-control">
              <option value="">All Tests</option>
            </select>
          </div>
          {% endif %}
          <div class="col-md-3">
            <label for="resultFilter">Result:</label>
            <select id="resultFilter" class="form-control">
              <option value="">All Results</option>
              <option value="PASS">PASS</option>
              <option value="FAIL">FAIL</option>
            </select>
          </div>
        </div>
        <div class="row mt-3">
          <div class="col-md-3">
            <label for="dateFromFilter">From Date:</label>
            <input type="date" id="dateFromFilter" class="form-control">
          </div>
          <div class="col-md-3">
            <label for="dateToFilter">To Date:</label>
            <input type="date" id="dateToFilter" class="form-control">
          </div>
        </div>
        <div class="row mt-3">
          <div class="col-md-12">
            <button id="clearFilters" class="btn btn-secondary btn-sm">Clear All Filters</button>
            <span id="filterStatus" class="ml-3 text-muted"></span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Results Table -->
      <table
        class="table table-striped table-bordered table-hover custom-table"
      >
        <thead>
          <tr>
            {% if query_test != 'ALL'%}
              <th class="sortable" data-column="0" data-sort="none">
                Unit <i class="fas fa-sort sort-icon"></i>
              </th>
              <th class="sortable" data-column="1" data-sort="none">
                Name <i class="fas fa-sort sort-icon"></i>
              </th>
              <th class="sortable" data-column="2" data-sort="none">
                Employee_No <i class="fas fa-sort sort-icon"></i>
              </th>
            {% else %}
              <th class="sortable" data-column="0" data-sort="none">
                Test <i class="fas fa-sort sort-icon"></i>
              </th>
            {% endif %}
            <th class="sortable" data-column="{% if query_test != 'ALL' %}3{% else %}1{% endif %}" data-sort="none">
              Result <i class="fas fa-sort sort-icon"></i>
            </th>
          </tr>
        </thead>
        <tbody>
          {% if query_test != 'ALL' %}
            {% for row in result_data %}
              <tr>
                {% for k,v in row.items() %}
                  {% if k == 'Result' and v != "[ NIL ]" %}
                    <td>
                      {% if v|length > 1 %}
                        {# Split cell for multiple results (FAIL and PASS) #}
                        <div style="display: flex; flex-direction: column; border: none;">
                          {% for result in v %}
                            <div style="{% if not loop.last %}border-bottom: 1px solid #dee2e6; padding-bottom: 5px; margin-bottom: 5px;{% endif %}">
                              <button
                                id="plain-{{ row.Employee_No }}-{{ loop.index }}"
                                class="btn btn-link p-0 plain-table-test-btn"
                                data-name="{{ row.Name }} ({{ row.Employee_No }})"
                                data-employee-no="{{ row.Employee_No }}"
                                data-staff-test='{{ result | tojson | safe }}'
                                data-test-form='{{ test_form | tojson | safe }}'
                              >
                                {% if result['pass'] %}
                                  <span
                                    style="color: green; text-decoration: none"
                                  ><strong>Pass on {{result['time'].split(' ')[0]}}</strong>
                                  </span>
                                {% else %}
                                  <span
                                    style="color: red; text-decoration: none"
                                  ><strong>FAIL on {{result['time'].split(' ')[0]}}</strong>
                                  </span>
                                {% endif %}
                              </button>
                            </div>
                          {% endfor %}
                        </div>
                      {% else %}
                        {# Single result #}
                        {% set result = v[0] %}
                        <button
                          id="plain-{{ row.Employee_No }}"
                          class="btn btn-link p-0 plain-table-test-btn"
                          data-name="{{ row.Name }} ({{ row.Employee_No }})"
                          data-employee-no="{{ row.Employee_No }}"
                          data-staff-test='{{ result | tojson | safe }}'
                          data-test-form='{{ test_form | tojson | safe }}'
                        >
                          {% if result['pass'] %}
                            <span
                              style="color: green; text-decoration: none"
                            ><strong>Pass on {{result['time'].split(' ')[0]}}</strong>
                            </span>
                          {% else %}
                            <span
                              style="color: red; text-decoration: none"
                            ><strong>FAIL on {{result['time'].split(' ')[0]}}</strong>
                            </span>
                          {% endif %}
                        </button>
                      {% endif %}
                    </td>
                  {% elif k == 'Result' and v == "[ NIL ]" %}
                    <td>
                      <button
                        id="plain-{{ row.Employee_No}}"
                        class="btn btn-link p-0 plain-table-test-btn"
                        data-name="{{ row.Name }} ({{ row.Employee_No }})"
                        data-employee-no="{{ row.Employee_No }}"
                        data-staff-test='[ NIL ]'
                        data-test-form='{}'
                      >
                        [ NIL ]
                      </button>
                    </td>
                  {% else %}
                    <td>{{ v }}</td>
                  {% endif %}
                {% endfor %}
              </tr>
            {% endfor %}


          {% else %}
          {# if query_test == 'ALL' #}

            {% for row in result_data %}
              {% if row['Result'] == '[ NIL ]' %}
              
                <tr>
                  <td>[ NIL ]</td>
                  <td>[ NIL ]</td>
                </tr>

              {% else %}
              {# if row['Result'] != '[ NIL ]' #}

                {% for result in row['Result'] %}
                  <tr>
                  {% for k,v in row.items() %}
                    {% if k == 'Result' and v != "[ NIL ]" %}
                      <td>
                        <span>{{ result['test_name'] | replace('_', ' ') }} | {{ result['test_title'] }}</span>
                      </td>
                      <td>
                        <button
                          id="plain-{{ row.Employee_No }}-{{ loop.index }}"
                          class="btn btn-link p-0 plain-table-test-btn"
                          data-name="{{ row.Name }} ({{ row.Employee_No }})"
                          data-employee-no="{{ row.Employee_No }}"
                          data-staff-test='{{ result | tojson | safe }}'
                          data-test-form='{{ test_form[result["test_name"]] | tojson | safe }}'
                        >
                          {% if result['pass'] %}
                          <span 
                            style="color: green; text-decoration: none"
                          ><strong>Pass on {{result['time'].split(' ')[0]}}</strong>
                          </span>
                          {% else %}
                          <span 
                            style="color: red; text-decoration: none"
                          ><strong>FAIL on {{result['time'].split(' ')[0]}}</strong>
                          </span>
                          {% endif %}
                        </button>
                      </td>
                    {% elif k == 'Result' and v == "[ NIL ]" %}
                      <td>
                        <span>[ NIL ]</span>
                      </td>
                      <td>
                        <span>[ NIL ]</span>
                      </td>
                    {% endif %} 
                    
                  {% endfor %}
                  {# end for k,v in row.items() #}

                  </tr>
                {% endfor %}
                {# end for result in row['Result'] #}

              {% endif %}
              {# end if row['Result'] == '[ NIL ]' #}

            {% endfor %}
            {# end for row in result_data #}

          {% endif %}
          {# end if query_test != 'ALL' #}

        </tbody>
      </table>


  {% if query_test != 'ALL' %}
    {% if test_form.info.form_type == 'mc' %}
      <!-- MC Result Modal -->
      <div
        class="modal fade"
        id="mc-result"
        tabindex="-1"
        aria-labelledby="mc-result-title"
        aria-hidden="true"
      >
        <div class="modal-dialog modal-fullscreen">
          <div class="modal-content">
            <div class="modal-header" style="background-color: #26a682">
              <h5 class="modal-title" id="mc-result-title" style="color: white">
                {{test_form.info.test_name}}
              </h5>
              <button
                type="button"
                class="btn-close"
                data-bs-dismiss="modal"
                aria-label="Close"
              ></button>
            </div>
            <div class="modal-body" id="mc-content">
              <form id="form-reply">
                {% for part,page in test_form.content.items() %}
                <div class="card mt-5">
                  <div class="card-header">
                    <h3>{{ page.header }}</h3>
                  </div>
                  <div class="card-body">
                    <div class="form-group">
                      {% if part == 'intro' %}
                      <label 
                        class="mt-3 mb-3" 
                      >{{ page.details|safe }}</label>
                      {% else %}
                      {% for id,item in page.details.items() %}
                      <label
                        class="question-text mt-3 mb-3"
                        for="{{ id }}"
                      ><strong>{{ id|replace('q','')|int }} ) <br/>{{ item.q|safe }}</strong></label>
                      
                      {% if item.opt|length > 0 %}
                      <div class="form-check" id="div-{{ id }}"></div>
                      {% else %}
                      <input
                        style="display: none"
                        type="text"
                        id="div-{{ id }}"
                        class="form-control"
                        disabled
                      />
                      {% endif %} 

                      {% if not loop.last %}
                      <!-- Add the horizontal line only if it's not the last question -->
                      <hr />
                      {% endif %}
                      
                      {% endfor %}
                      {% endif %} 
                    </div>

                  </div>
                </div>
                {% endfor %}
              </form>
            </div>
            <div class="modal-footer">
              <button
                type="button"
                class="btn btn-secondary"
                data-bs-dismiss="modal"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      </div>
    {% else %}
      <!-- Checklist Result Modal -->
      <div 
        class="modal fade" 
        id="checklist-result" 
        tabindex="-1" 
        aria-labelledby="checklist-result-title" 
        aria-hidden="true"
      >
        <div class="modal-dialog modal-fullscreen">
          <div class="modal-content">
            <div class="modal-header" style="background-color: #26a682">
              <h5 class="modal-title" id="checklist-result-title" style="color: white"></h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="checklist-content">
              <!-- Content will be dynamically injected here -->
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
          </div>
        </div>
      </div>
    {% endif %}

  {% else %}

    <!-- MC Result Modal -->
    <div
      class="modal fade"
      id="mc-result"
      tabindex="-1"
      aria-labelledby="mc-result-title"
      aria-hidden="true"
    >
      <div class="modal-dialog modal-fullscreen">
        <div class="modal-content">
          <div class="modal-header" style="background-color: #26a682">
            <h5 class="modal-title" id="mc-result-title" style="color: white"></h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
            ></button>
          </div>
          <div class="modal-body" id="mc-content">
            <!-- Content will be dynamically injected here -->
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
          </div>
        </div>
      </div>
    </div>


    <!-- Checklist Result Modal -->
    <div 
      class="modal fade" 
      id="checklist-result" 
      tabindex="-1" 
      aria-labelledby="checklist-result-title" 
      aria-hidden="true"
    >
      <div class="modal-dialog modal-fullscreen">
        <div class="modal-content">
          <div class="modal-header" style="background-color: #26a682">
            <h5 class="modal-title" id="checklist-result-title" style="color: white"></h5>
            <button 
              type="button" 
              class="btn-close" 
              data-bs-dismiss="modal" 
              aria-label="Close"
            ></button>
          </div>
          <div class="modal-body" id="checklist-content">
            <!-- Content will be dynamically injected here -->
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
          </div>
        </div>
      </div>
    </div>



  {% endif %}

  <!-- Confirm Redirect Modal -->
  <div
    class="modal fade"
    id="confirm-redirect"
    tabindex="-1"
    aria-labelledby="confirm-redirect-title"
    aria-hidden="true"
  >
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="confirm-redirect-title">Confirmation</h5>
          <button
            type="button"
            class="btn-close"
            data-bs-dismiss="modal"
            aria-label="Close"
          ></button>
        </div>
        <div class="modal-body">
          <p id="fill-in-assessment">
            Fill in assessment form for this operator?
          </p>
          <!-- Hidden form -->
          <form id="redirect-form" method="POST" action="">
            <input type="hidden" id="redirect-employeeNo" name="redirect-employeeNo" />
            <input type="hidden" id="redirect-test" name="redirect-test" />
          </form>
        </div>
        <div class="modal-footer">
          <button
            type="button"
            class="btn btn-secondary"
            data-bs-dismiss="modal"
          >
            Cancel
          </button>
          <button type="button" class="btn btn-primary" id="confirm-rdr-btn">
            Confirm
          </button>
        </div>
      </div>
    </div>
  </div>
</div>


<div id="data-container"
  data-query-by="{{ query_by }}"
  data-query-test="{{ query_test }}"
  data-result-data='{{ result_data | tojson | safe }}'
  data-test-form='{{ test_form | tojson | safe }}'
  data-url-for-view="{{ url_for('view') }}"
></div>

<script type="module" src="{{ url_for('static', filename='js/result.js') }}"></script>

{% endblock %}
