<!-- ARCHIVED AGGrid-related HTML from result.html -->
<!-- This file contains all the AGGrid HTML components that were removed from result.html -->
<!-- Keep this file for reference or future restoration if needed -->

<!-- AGGrid CDN -->
<script src="https://cdn.jsdelivr.net/npm/ag-grid-community/dist/ag-grid-community.min.js"></script>

<!-- AGGrid CSS Styles -->
<style>
  #grid-container {
    width: 100%;
    height: 100%;
    min-height: 500px;
  }
  #grid {
    width: 100%;
    height: 500px;
    min-height: 500px;
  }
</style>

<!-- Tab Navigation (Interactive vs Plain) -->
<ul class="nav nav-tabs" role="tablist">
  <li class="nav-item">
    <a
      class="nav-link active"
      data-toggle="tab"
      href="#result-ag"
      id="tab-ag"
      role="tab"
      >Interactive</a
    >
  </li>
  <li class="nav-item">
    <a
      class="nav-link"
      data-toggle="tab"
      href="#result-tr"
      id="tab-tr"
      role="tab"
      >Plain</a
    >
  </li>
</ul>

<!-- AGGrid Tab Content -->
<div class="tab-pane fade show active" id="result-ag" role="tabpanel">
  <div id="grid-container">
    <div id="grid" class="ag-theme-alpine"></div>
  </div>
</div>
