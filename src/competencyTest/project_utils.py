from competencyTest import db
import re

def get_dept_and_ward(session, show_invisible=False):

    # Get the entire permission database
    permissions = dict(db.child("permission").get().val())

    # Extract the allowed departments for the current user
    allowed_departments = [
        data['access'] for data in permissions.values() 
        if data['email'] == session['user_email']
    ]

    allowed_departments = allowed_departments[0] if allowed_departments else []


    if 'ALL' in allowed_departments:

        if show_invisible:
            allowed_departments = list(set([
                data['dept'] 
                for data in permissions.values()
            ]))
        
        else:
            allowed_departments = list(set([
                data['dept'] 
                for data in permissions.values()
                if 'invisible' not in data
            ]))

    allowed_departments.sort()

    # Create the units_by_department dictionary
    units_by_department = {}
    if show_invisible:
        for data in permissions.values():
            units_by_department.setdefault(data['dept'], []).append(data['unit'])
    else:
        for data in permissions.values():
            if 'invisible' not in data.keys():
                units_by_department.setdefault(data['dept'], []).append(data['unit'])


    return allowed_departments, units_by_department

def get_all_depts_units():

    permissions = dict(db.child("permission").get().val())

    departments = list(set([
        data['dept'] for data in permissions.values()
        if 'invisible' not in data
    ]))

    departments.sort()

    units_by_department = {}
    for data in permissions.values():
        if 'invisible' not in data:
            units_by_department.setdefault(data['dept'], []).append(data['unit'])

    return departments, units_by_department
    
def get_tests_overview():

    tests = dict(db.child("test").get().val())
    tests_overview = {
        test_name: {
            test['info']['version']: {
                'title': test['content'].get('intro', {}).get('header', ''),
                'version': test['info']['version'],
                'version_name': test['info']['version_name'],
                'form': test['info']['form_type']
            }
            for test in test_versions.values()
        }
        for test_name, test_versions in tests.items()
    }


    return tests_overview

def get_latest_form(test_name):
    
    test_forms = dict(db.child('test').child(test_name).get().val())
    versions = list(test_forms.keys())
    versions.sort(reverse=True)
    latest_ver = versions[0]
    test_content = test_forms[latest_ver]['content']

    # reorder the keys
    test_content = {
        k: test_content.get(k, {})
        for k in ['intro', 'basic_entry', 'questions', 'additional_entry']
    }

    test_info = test_forms[latest_ver]['info']

    return test_content, test_info

def convert_user_index_to_key(user_index):
    """Convert user input like '3.1' or '4.1.1' to database key like 'q03_1' or 'q04_1_1'"""
    if not user_index or user_index.strip() == '':
        return ''

    # Remove trailing dot
    cleaned = user_index.strip().rstrip('.')

    # Split by dots
    parts = cleaned.split('.')

    # Convert first part to zero-padded
    main_part = f"{int(parts[0]):02d}"

    # Join remaining parts with underscores
    sub_parts = '_'.join(parts[1:]) if len(parts) > 1 else ''

    # Combine
    return f"q{main_part}" + (f"_{sub_parts}" if sub_parts else "")

def formulate_checklist(data, ver):
    print(data)
    new_checklist = {}
    new_checklist['info'] = {
        'test_name': data['test-name'].strip(),
        'form_type': 'checklist',
        'version': ver.strip(),
        'version_name': data['version-name'].strip(),
        'passing_criteria': data['passing-criteria'].strip(),
        'option_setting': data['option-setting'].strip()
    }

    # Build items dictionary using user-provided indices
    items_dict = {}
    for k, v in data.items():
        if k.startswith('item-') and not k.endswith(('-essential', '-type', '-index')):
            # Extract item number from key like 'item-1' -> '1'
            parts = k.split('-')
            if len(parts) >= 2:
                try:
                    item_num = parts[1]

                    # Get user-provided index
                    user_index = data.get(f'item-index-{item_num}', '')
                    if user_index:
                        database_key = convert_user_index_to_key(user_index)
                    else:
                        # Fallback to old system
                        database_key = f'q{int(item_num):02d}'

                    # Get essential status
                    essential = data.get(f'item-essential-{item_num}') == 'on'

                    # Get type (subtitle)
                    item_type = data.get(f'item-type-{item_num}', '')

                    item_data = {
                        'text': v.strip(),
                        'essential': essential
                    }

                    # Add subtitle based on type selection
                    if item_type and item_type != '':
                        item_data['subtitle'] = int(item_type)

                    items_dict[database_key] = item_data
                except (ValueError, IndexError):
                    # Skip invalid item keys
                    continue

    new_checklist['content'] = {

        'intro':{
            'header': data['intro-header'].strip(),
            'details': data['intro-details'].strip()
        },
        'questions':{
            'header': data['questions-header'].strip(),
            'itemcol': data['col-item'].strip(),
            'details': {
                'items': items_dict,
                'options':{
                    f'o{int(k.split("-")[1]):02}' : {
                        'name': v.strip(),
                        'type': data[f'{k}-type'],
                        'count_score': True if data.get(f'cs-{k.split("-")[1]}', False) else False
                    }
                    for k,v in data.items()
                    if k.startswith('ans-')
                    and not k.endswith('-type')
                }
            }
        }

    }

    new_checklist['content']['basic_entry'] = {
        'header': data.get('basic-entry-header','').strip(),
        'details': {}
    }
    new_checklist['content']['additional_entry'] = {
        'header': data.get('additional-entry-header','').strip(),
        'details': {}
    }
    for k,v in data.items():
        if k.startswith('new-basic-entry-input') or k.startswith('new-additional-entry-input'):
            prefix = k.split('-input')[0]
            idx = k[-2:]
            entry_name = prefix.replace('new-','').replace('-','_')
            entry_key = f'{entry_name}_{idx}'
            if data[f'{prefix}-select-{idx}'] == 'custom_options':
                new_checklist['content'][entry_name]['details'][entry_key] = {
                    'title': v.strip(),
                    'input': [x.strip() for x in data[f'{prefix}-select-{idx}-custom-options'].split('|')]
                }
            else:
                new_checklist['content'][entry_name]['details'][entry_key] = {
                    'title': v.strip(),
                    'input': data[f'{prefix}-select-{idx}']
                }
            input_required = k.replace('input','required')
            if input_required in data:
                new_checklist['content'][entry_name]['details'][entry_key]['required'] = True

    return new_checklist

def formulate_mc(data, ver):
    new_mc = {}
    new_mc['info'] = {
        'test_name': data['test-name'].replace(' ','_'),
        'form_type': 'mc',
        'version': ver,
        'version_name': data['version-name'],
        'pass_score': int(data['pass-score']),
        'total_score': int(data['total-score']),
        'score_per_question': int(data['score-per-q'])
    }

    pattern__n = r'_\d$'
    pattern_qXX = r'^q\d\d$'

    new_mc['content'] = {
    
        'intro':{
            'header': data['intro-header'],
            'details': data['intro-details']
        },
        'questions':{
            'header': data['questions-header'],
            'details': {
                k:{
                    'ans': data[f'{k}-ans'].strip(),
                    'id': k,
                    'opt':[
                        data[option].strip() for option in data.keys() 
                        if option.startswith(k)
                        and re.search(pattern__n, option)
                    ],
                    'q': v.strip()
                } 
                for k,v in data.items() 
                if re.match(pattern_qXX, k)
            }
                
        }

    }

    return new_mc


def formulate_checklist_edit(data, ver, original_test):
    """
    Modified version of formulate_checklist for editing existing tests.
    Preserves the original structure while updating editable fields.
    """
    print("Edit data:", data)

    # Start with the original test structure
    updated_checklist = original_test.copy()

    # Update info section
    updated_checklist['info']['version_name'] = data.get('version-name', '').strip()
    updated_checklist['info']['passing_criteria'] = data.get('passing-criteria', '').strip()

    # Update content sections
    if 'content' not in updated_checklist:
        updated_checklist['content'] = {}

    # Update intro
    updated_checklist['content']['intro'] = {
        'header': data.get('intro-header', '').strip(),
        'details': data.get('intro-details', '').strip()
    }

    # Update basic_entry
    if 'basic_entry' not in updated_checklist['content']:
        updated_checklist['content']['basic_entry'] = {'header': '', 'details': {}}
    updated_checklist['content']['basic_entry']['header'] = data.get('basic-entry-header', '').strip()

    # Update basic_entry details
    basic_entry_details = {}
    for k, v in data.items():
        if k.startswith('basic-entry-title-'):
            entry_key = k.replace('basic-entry-title-', '')
            entry_type = data.get(f'basic-entry-type-{entry_key}', 'text')
            entry_required = data.get(f'basic-entry-required-{entry_key}') == 'on'

            if entry_type == 'custom_options':
                options_str = data.get(f'basic-entry-options-{entry_key}', '')
                entry_input = [opt.strip() for opt in options_str.split('|') if opt.strip()]
            else:
                entry_input = entry_type

            basic_entry_details[entry_key] = {
                'title': v.strip(),
                'input': entry_input,
                'required': entry_required
            }
    updated_checklist['content']['basic_entry']['details'] = basic_entry_details

    # Update questions section
    if 'questions' not in updated_checklist['content']:
        updated_checklist['content']['questions'] = {'header': '', 'itemcol': '', 'details': {'items': {}, 'options': {}}}

    updated_checklist['content']['questions']['header'] = data.get('questions-header', '').strip()
    updated_checklist['content']['questions']['itemcol'] = data.get('col-item', '').strip()

    # Update column options
    column_options = {}
    for k, v in data.items():
        if k.startswith('column-name-'):
            column_key = k.replace('column-name-', '')
            column_type = data.get(f'column-type-{column_key}', 'checkbox')
            column_score = data.get(f'column-score-{column_key}') == 'on'

            column_options[column_key] = {
                'name': v.strip(),
                'type': column_type,
                'count_score': column_score
            }
    updated_checklist['content']['questions']['details']['options'] = column_options

    # Update items - handle new index fields and preserve original keys with underscores and subtitles
    items_dict = {}
    for k, v in data.items():
        if k.startswith('item-text-'):
            # Extract the original key (preserving underscores)
            original_key = k.replace('item-text-', '')

            # Get the new index from form data
            index_key = f'item-index-{original_key}'
            user_index = data.get(index_key, '').strip()

            # Convert user index to database key, or keep original if no index provided
            if user_index:
                new_key = convert_index_to_key(user_index)
            else:
                new_key = original_key

            # Get essential status
            essential = data.get(f'item-essential-{original_key}') == 'on'

            # Get subtitle if present
            subtitle_value = data.get(f'item-subtitle-{original_key}')

            item_data = {
                'text': v.strip(),
                'essential': essential
            }

            # Add subtitle if it exists (can be 1, 2, or 3)
            if subtitle_value and subtitle_value != '':
                item_data['subtitle'] = int(subtitle_value)

            items_dict[new_key] = item_data

    if 'items' not in updated_checklist['content']['questions']['details']:
        updated_checklist['content']['questions']['details']['items'] = {}

    # Update with new items data
    updated_checklist['content']['questions']['details']['items'] = items_dict

    # Update additional_entry
    if 'additional_entry' not in updated_checklist['content']:
        updated_checklist['content']['additional_entry'] = {'header': '', 'details': {}}
    updated_checklist['content']['additional_entry']['header'] = data.get('additional-entry-header', '').strip()

    # Update additional_entry details
    additional_entry_details = {}
    for k, v in data.items():
        if k.startswith('additional-entry-title-'):
            entry_key = k.replace('additional-entry-title-', '')
            entry_type = data.get(f'additional-entry-type-{entry_key}', 'text')
            entry_required = data.get(f'additional-entry-required-{entry_key}') == 'on'

            if entry_type == 'custom_options':
                options_str = data.get(f'additional-entry-options-{entry_key}', '')
                entry_input = [opt.strip() for opt in options_str.split('|') if opt.strip()]
            else:
                entry_input = entry_type

            additional_entry_details[entry_key] = {
                'title': v.strip(),
                'input': entry_input,
                'required': entry_required
            }
    updated_checklist['content']['additional_entry']['details'] = additional_entry_details

    return updated_checklist

def formulate_mc_edit(data, ver, original_test):
    """
    Modified version of formulate_mc for editing existing tests.
    Preserves the original structure while updating editable fields.
    """
    print("Edit MC data:", data)

    # Start with the original test structure
    updated_mc = original_test.copy()

    # Update info section
    updated_mc['info']['version_name'] = data.get('version-name', '').strip()
    updated_mc['info']['pass_score'] = int(data.get('pass-score', 0))
    updated_mc['info']['total_score'] = int(data.get('total-score', 0))
    updated_mc['info']['score_per_question'] = int(data.get('score-per-q', 0))

    # Update content sections
    if 'content' not in updated_mc:
        updated_mc['content'] = {}

    # Update intro
    updated_mc['content']['intro'] = {
        'header': data.get('intro-header', '').strip(),
        'details': data.get('intro-details', '').strip()
    }

    # Update questions section
    if 'questions' not in updated_mc['content']:
        updated_mc['content']['questions'] = {'header': '', 'details': {}}

    updated_mc['content']['questions']['header'] = data.get('questions-header', '').strip()

    # Update questions details
    import re
    pattern_qXX = r'^q\d+$'

    # Process questions with new index handling
    questions_dict = {}
    for k, v in data.items():
        if re.match(pattern_qXX, k):
            # This is a question text - get the original key
            original_key = k

            # Get the new index from form data
            index_key = f'question-index-{original_key}'
            user_index = data.get(index_key, '').strip()

            # Convert user index to database key, or keep original if no index provided
            if user_index:
                new_key = convert_index_to_key(user_index)
            else:
                new_key = original_key

            # Build question data
            question_data = {
                'q': v.strip(),
                'id': new_key
            }

            # Get answer
            ans_key = f'{original_key}-ans'
            if ans_key in data:
                question_data['ans'] = data[ans_key].strip()

            # Get options
            options = []
            for option_key in data.keys():
                if option_key.startswith(f'{original_key}_') and option_key != ans_key:
                    options.append(data[option_key].strip())

            if options:
                question_data['opt'] = options

            questions_dict[new_key] = question_data

    if 'details' not in updated_mc['content']['questions']:
        updated_mc['content']['questions']['details'] = {}

    # Update with new questions data
    updated_mc['content']['questions']['details'] = questions_dict

    return updated_mc

def generate_question_mapping(original_test, form_data, test_type):
    """
    Generate a mapping between original question keys and new question keys
    to help update staff assessment results when tests are edited.
    """
    mapping = {}

    if test_type == 'checklist':
        # Get original question keys
        original_questions = original_test.get('content', {}).get('questions', {}).get('details', {}).get('items', {})

        # Process new question data from form
        for k, v in form_data.items():
            if k.startswith('item-text-'):
                # Extract the key from form field name
                key_part = k.replace('item-text-', '')

                # Get the index from form data
                index_key = f'item-index-{key_part}'
                if index_key in form_data:
                    user_index = form_data[index_key].strip()
                    if user_index:
                        # Convert user index to database key
                        new_key = convert_index_to_key(user_index)

                        # Find matching original question by text content
                        question_text = v.strip()
                        for orig_key, orig_data in original_questions.items():
                            if orig_data.get('text', '').strip() == question_text:
                                mapping[orig_key] = new_key
                                break

    elif test_type == 'mc':
        # Get original MC questions
        original_questions = original_test.get('content', {}).get('questions', {}).get('details', {})

        # Process new MC question data with index handling
        import re
        pattern_qXX = r'^q\d+$'

        for k, v in form_data.items():
            if re.match(pattern_qXX, k):
                # This is a question key - get the original key
                original_key = k

                # Get the new index from form data
                index_key = f'question-index-{original_key}'
                if index_key in form_data:
                    user_index = form_data[index_key].strip()
                    if user_index:
                        # Convert user index to database key
                        new_key = convert_index_to_key(user_index)

                        # Find matching original question by text content
                        question_text = v.strip()
                        for orig_key, orig_data in original_questions.items():
                            if orig_data.get('q', '').strip() == question_text:
                                mapping[orig_key] = new_key
                                break

    return mapping

def convert_index_to_key(user_index):
    """
    Convert user index like "3.1" or "4.1.1" to database key like "q03_1" or "q04_1_1"
    This is the same function used in new_checklist.js
    """
    if not user_index or user_index.strip() == '':
        return ''

    # Remove trailing dot if present: "1." → "1"
    cleaned = user_index.strip().rstrip('.')

    # Split by dots: "4.1.1" → ["4", "1", "1"]
    parts = cleaned.split('.')

    # Convert first part to zero-padded: "4" → "04"
    main_part = parts[0].zfill(2)

    # Join remaining parts with underscores: ["1", "1"] → "_1_1"
    sub_parts = '_'.join(parts[1:]) if len(parts) > 1 else ''

    # Combine: "q04_1_1"
    return 'q' + main_part + ('_' + sub_parts if sub_parts else '')

def check_key_contains_string(dictionary, target_string):
    for key in dictionary:
        if target_string in key:
            return True
    return False