# Manual Testing Guide for Staff Assessment Editing

This guide provides step-by-step instructions to manually test the staff assessment editing functionality.

## Prerequisites

1. Make sure the Flask application is running
2. You have admin access to the system
3. You have access to the Firebase database

## Test Scenario 1: Basic Question Index Changes

### Step 1: Create a Test Checklist
1. Go to Admin page
2. Click "Create" under "New Test"
3. Create a new checklist with these questions:
   - Index: `1`, Text: `Check patient identification`, Type: `question`
   - Index: `2`, Text: `Verify medication dosage`, Type: `question`
   - Index: `3.1`, Text: `Document vital signs`, Type: `inner question`
   - Index: `4`, Text: `Clean work area`, Type: `question`
4. Save the test as `TEST_MANUAL_001`

### Step 2: Create Staff Assessment Data
1. Go to the appropriate form (Checklist or PCA)
2. Fill out an assessment for a test staff member using the test you just created
3. Submit the assessment
4. Verify the result appears in the Results page

### Step 3: Edit the Test
1. Go to Admin page
2. Click "Choose Test" under "Edit Test"
3. Select your test (`TEST_MANUAL_001`)
4. Make these changes:
   - Change question index `3.1` to `5.1` (Document vital signs)
   - Change question index `4` to `3` (Clean work area)
   - Keep other questions unchanged
5. Save the changes
6. You should see a progress modal saying "Updating staff assessment records..."
7. You should see a success message with the number of updated records

### Step 4: Verify the Results
1. Go to Results page
2. Find the staff member's assessment
3. Click on their result to view the modal
4. Verify that:
   - All questions still display correctly
   - The assessment data is preserved
   - No errors occur when viewing the result

## Test Scenario 2: Question Text Changes

### Step 1: Edit Question Text
1. Go to Admin → Edit Test
2. Select your test
3. Change the text of one question (e.g., "Check patient identification" → "Verify patient identification")
4. Keep the index the same
5. Save the changes

### Step 2: Verify Results Still Work
1. Go to Results page
2. View the staff assessment
3. Verify the updated question text appears correctly
4. Verify all data is preserved

## Test Scenario 3: Adding New Questions

### Step 1: Add New Questions
1. Edit your test
2. Add a new question with index `6`
3. Save the changes

### Step 2: Verify Existing Results
1. Check that existing staff assessments still work
2. The new question should not affect old results
3. New assessments should include the new question

## Test Scenario 4: Multiple Staff Members

### Step 1: Create Multiple Assessments
1. Create assessments for multiple staff members using the same test
2. Use different staff types (nurse, pca, temp_staff)

### Step 2: Edit the Test
1. Make significant changes to question indices
2. Save the changes

### Step 3: Verify All Results
1. Check that all staff assessments were updated
2. Verify the success message shows the correct count
3. Test viewing results for different staff members

## Expected Behaviors

### ✅ What Should Work:
- Question index changes are reflected in staff results
- Question text changes are preserved
- All existing assessment data is maintained
- Result viewing continues to work normally
- Progress feedback is shown during updates
- Success message shows update count

### ❌ What Should NOT Happen:
- Staff assessment data should not be lost
- Result viewing should not break
- No JavaScript errors in browser console
- No server errors in application logs

## Troubleshooting

### If Results Don't Display Correctly:
1. Check browser console for JavaScript errors
2. Verify the question mapping was generated correctly
3. Check that staff assessment keys were updated properly

### If No Staff Records Are Updated:
1. Verify staff have existing assessments for the test
2. Check that the test name and version match exactly
3. Ensure admin permissions are correct

### If Errors Occur:
1. Check the Flask application logs
2. Verify Firebase database connectivity
3. Check that all required fields are present in the form data

## Database Verification

You can manually check the database to verify updates:

1. **Before editing**: Note the question keys in staff assessments
2. **After editing**: Verify the keys have been updated according to the mapping
3. **Check collections**: Verify updates in `nurse`, `pca`, and `temp_staff` collections

## Performance Testing

For large datasets:
1. Create multiple staff assessments (10-20)
2. Edit a test with many questions
3. Verify the update completes in reasonable time
4. Check that the progress modal provides good feedback

## Edge Cases to Test

1. **Empty assessments**: Staff with no assessment data
2. **Partial assessments**: Staff with incomplete assessment data
3. **Multiple versions**: Staff with assessments for different test versions
4. **Special characters**: Question text with special characters or unicode
5. **Long question text**: Very long question descriptions

## Success Criteria

The test is successful if:
- ✅ All existing staff assessment data is preserved
- ✅ Question key mappings are updated correctly
- ✅ Result viewing works without errors
- ✅ Progress feedback is provided to users
- ✅ Success messages are accurate
- ✅ No data corruption occurs
- ✅ Performance is acceptable for typical dataset sizes
