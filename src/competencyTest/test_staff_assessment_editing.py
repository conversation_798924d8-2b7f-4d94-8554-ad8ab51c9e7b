#!/usr/bin/env python3
"""
Test script for staff assessment editing functionality.
This script tests the complete flow of editing tests and updating staff assessments.
"""

import sys
import os
import json
from datetime import datetime
import pytz

# Add the project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from project_utils import generate_question_mapping, convert_index_to_key, formulate_checklist_edit
from config import FIREBASE_CONFIG
import pyrebase

# Initialize Firebase
firebase = pyrebase.initialize_app(FIREBASE_CONFIG)
db = firebase.database()

def create_test_data():
    """Create test data for testing the staff assessment editing functionality."""
    
    # Test checklist structure
    test_checklist = {
        'info': {
            'test_name': 'TEST_CHECKLIST_001',
            'form_type': 'checklist',
            'version': 'v001',
            'version_name': 'Test Version 1',
            'passing_criteria': '80%',
            'option_setting': 'standard'
        },
        'content': {
            'intro': {
                'header': 'Test Checklist',
                'details': 'This is a test checklist for testing purposes.'
            },
            'questions': {
                'header': 'Assessment Questions',
                'itemcol': 'Assessment Items',
                'details': {
                    'items': {
                        'q01': {
                            'text': 'Check patient identification',
                            'essential': True
                        },
                        'q02': {
                            'text': 'Verify medication dosage',
                            'essential': True
                        },
                        'q03_1': {
                            'text': 'Document vital signs',
                            'essential': False
                        },
                        'q04': {
                            'text': 'Clean work area',
                            'essential': False
                        }
                    },
                    'options': {
                        'o01': {
                            'name': '滿意',
                            'type': 'checkbox',
                            'count_score': True
                        },
                        'o02': {
                            'name': '不滿意',
                            'type': 'checkbox',
                            'count_score': False
                        }
                    }
                }
            },
            'basic_entry': {
                'header': 'Basic Information',
                'details': {
                    'basic_entry_01': {
                        'title': '指導員姓名',
                        'input': 'text',
                        'required': True
                    },
                    'basic_entry_02': {
                        'title': '指導員職級',
                        'input': 'text',
                        'required': True
                    }
                }
            },
            'additional_entry': {
                'header': 'Additional Information',
                'details': {
                    'additional_entry_01': {
                        'title': '備註',
                        'input': 'textarea',
                        'required': False
                    }
                }
            }
        }
    }
    
    # Test staff assessment data
    test_staff_data = {
        'TEST001': {
            'Name': 'Test Nurse',
            'Employee_No': 'TEST001',
            'Department': 'ICU',
            'Unit': 'ICU-A',
            'Assessments': {
                'TEST_CHECKLIST_001': {
                    'v001': {
                        'version': 'v001',
                        'result': {
                            'q01': {'o01': 'true'},
                            'q02': {'o01': 'true'},
                            'q03_1': {'o02': 'true'},
                            'q04': {'o01': 'true'},
                            'basic_entry_01': 'Supervisor Name',
                            'basic_entry_02': 'Senior Nurse',
                            'additional_entry_01': 'Good performance'
                        },
                        'pass': True,
                        'time': '2024-01-15 10:30'
                    }
                }
            }
        }
    }
    
    return test_checklist, test_staff_data

def test_convert_index_to_key():
    """Test the convert_index_to_key function."""
    print("Testing convert_index_to_key function...")
    
    test_cases = [
        ('1', 'q01'),
        ('1.', 'q01'),
        ('2.1', 'q02_1'),
        ('3.1.1', 'q03_1_1'),
        ('10', 'q10'),
        ('15.2.3', 'q15_2_3'),
        ('', ''),
        ('  ', '')
    ]
    
    for input_val, expected in test_cases:
        result = convert_index_to_key(input_val)
        status = "✓" if result == expected else "✗"
        print(f"  {status} '{input_val}' -> '{result}' (expected: '{expected}')")
        if result != expected:
            return False
    
    print("  All convert_index_to_key tests passed!")
    return True

def test_generate_question_mapping():
    """Test the generate_question_mapping function."""
    print("\nTesting generate_question_mapping function...")
    
    test_checklist, _ = create_test_data()
    
    # Simulate form data with changed question indices
    form_data = {
        'item-text-1': 'Check patient identification',  # q01 -> q01 (no change)
        'item-index-1': '1',
        'item-text-2': 'Verify medication dosage',      # q02 -> q02 (no change)
        'item-index-2': '2',
        'item-text-3': 'Document vital signs',          # q03_1 -> q05_1 (changed)
        'item-index-3': '5.1',
        'item-text-4': 'Clean work area',               # q04 -> q03 (changed)
        'item-index-4': '3',
        'item-text-5': 'New assessment item',           # New item
        'item-index-5': '4'
    }
    
    mapping = generate_question_mapping(test_checklist, form_data, 'checklist')
    
    expected_mapping = {
        'q01': 'q01',      # No change
        'q02': 'q02',      # No change
        'q03_1': 'q05_1',  # Changed from 3.1 to 5.1
        'q04': 'q03'       # Changed from 4 to 3
    }
    
    print(f"  Generated mapping: {mapping}")
    print(f"  Expected mapping: {expected_mapping}")
    
    success = True
    for orig_key, expected_new_key in expected_mapping.items():
        if mapping.get(orig_key) != expected_new_key:
            print(f"  ✗ Mapping mismatch for {orig_key}: got {mapping.get(orig_key)}, expected {expected_new_key}")
            success = False
        else:
            print(f"  ✓ {orig_key} -> {mapping.get(orig_key)}")
    
    if success:
        print("  All generate_question_mapping tests passed!")
    
    return success

def test_staff_assessment_update_simulation():
    """Simulate the staff assessment update process."""
    print("\nTesting staff assessment update simulation...")
    
    test_checklist, test_staff_data = create_test_data()
    
    # Question mapping from the previous test
    question_mapping = {
        'q01': 'q01',      # No change
        'q02': 'q02',      # No change
        'q03_1': 'q05_1',  # Changed
        'q04': 'q03'       # Changed
    }
    
    # Simulate updating staff assessment
    employee_data = test_staff_data['TEST001']
    test_name = 'TEST_CHECKLIST_001'
    version = 'v001'
    
    if test_name in employee_data['Assessments'] and version in employee_data['Assessments'][test_name]:
        original_result = employee_data['Assessments'][test_name][version]['result']
        print(f"  Original result: {original_result}")
        
        # Update the result keys based on question mapping
        updated_result = {}
        for old_key, value in original_result.items():
            new_key = question_mapping.get(old_key, old_key)
            updated_result[new_key] = value
            if old_key != new_key:
                print(f"  ✓ Updated key: {old_key} -> {new_key}")
            else:
                print(f"  ✓ Kept key: {old_key}")
        
        print(f"  Updated result: {updated_result}")
        
        # Verify expected changes
        expected_keys = ['q01', 'q02', 'q05_1', 'q03', 'basic_entry_01', 'basic_entry_02', 'additional_entry_01']
        actual_keys = list(updated_result.keys())
        
        missing_keys = set(expected_keys) - set(actual_keys)
        extra_keys = set(actual_keys) - set(expected_keys)
        
        if not missing_keys and not extra_keys:
            print("  ✓ All keys updated correctly!")
            return True
        else:
            if missing_keys:
                print(f"  ✗ Missing keys: {missing_keys}")
            if extra_keys:
                print(f"  ✗ Extra keys: {extra_keys}")
            return False
    
    return False

def run_integration_test():
    """Run a complete integration test with actual database operations."""
    print("\nRunning integration test with database...")
    
    test_checklist, test_staff_data = create_test_data()
    test_name = 'TEST_CHECKLIST_001'
    version = 'v001'
    
    try:
        # 1. Create test data in database
        print("  1. Creating test data in database...")
        db.child('test').child(test_name).child(version).set(test_checklist)
        db.child('nurse').child('TEST001').set(test_staff_data['TEST001'])
        print("     ✓ Test data created")
        
        # 2. Verify data was created
        print("  2. Verifying test data...")
        stored_test = db.child('test').child(test_name).child(version).get().val()
        stored_staff = db.child('nurse').child('TEST001').get().val()
        
        if stored_test and stored_staff:
            print("     ✓ Test data verified")
        else:
            print("     ✗ Test data verification failed")
            return False
        
        # 3. Simulate question mapping and staff update
        print("  3. Simulating staff assessment update...")
        question_mapping = {
            'q01': 'q01',
            'q02': 'q02', 
            'q03_1': 'q05_1',
            'q04': 'q03'
        }
        
        # Update staff assessment
        original_result = stored_staff['Assessments'][test_name][version]['result']
        updated_result = {}
        
        for old_key, value in original_result.items():
            new_key = question_mapping.get(old_key, old_key)
            updated_result[new_key] = value
        
        # Update in database
        stored_staff['Assessments'][test_name][version]['result'] = updated_result
        db.child('nurse').child('TEST001').set(stored_staff)
        print("     ✓ Staff assessment updated")
        
        # 4. Verify the update
        print("  4. Verifying staff assessment update...")
        updated_staff = db.child('nurse').child('TEST001').get().val()
        final_result = updated_staff['Assessments'][test_name][version]['result']
        
        expected_keys = ['q01', 'q02', 'q05_1', 'q03', 'basic_entry_01', 'basic_entry_02', 'additional_entry_01']
        if all(key in final_result for key in expected_keys):
            print("     ✓ Staff assessment update verified")
            print(f"     Final result keys: {list(final_result.keys())}")
            return True
        else:
            print("     ✗ Staff assessment update verification failed")
            return False
            
    except Exception as e:
        print(f"     ✗ Integration test failed: {e}")
        return False
    
    finally:
        # Clean up test data
        print("  5. Cleaning up test data...")
        try:
            db.child('test').child(test_name).remove()
            db.child('nurse').child('TEST001').remove()
            print("     ✓ Test data cleaned up")
        except:
            print("     ! Warning: Could not clean up test data")

def test_real_data_structure():
    """Test with the actual database structure from exported files."""
    print("\nTesting with real database structure...")

    # Real test structure from PCA_002.json
    real_test = {
        'content': {
            'questions': {
                'details': {
                    'items': {
                        'q01': {
                            'essential': True,
                            'text': '預備需用物品前、接觸病人前後，均要清潔雙手。'
                        },
                        'q02': {
                            'essential': False,
                            'text': '按指示預備所需物品及病床。'
                        },
                        'q03': {
                            'essential': True,
                            'text': '核對病人身份，向病人告知來意並解釋程序， 以取得病人合作。'
                        },
                        'q11': {
                            'essential': True,
                            'text': '如有異常情況，必須向主責護士報告。'
                        }
                    }
                }
            }
        }
    }

    # Real staff assessment structure from pca_staff.json
    real_staff_result = {
        'q01': {'o01': 'true'},
        'q02': {'o01': 'true'},
        'q03': {'o01': 'true'},
        'q11': {'o01': 'true'},
        'basic_entry_01': 'Eva Leung',
        'basic_entry_02': 'WM / ANC',
        'additional_entry_01': '滿意'
    }

    # Simulate form data that reorders questions
    form_data = {
        'item-text-1': '預備需用物品前、接觸病人前後，均要清潔雙手。',  # q01 -> q01
        'item-index-1': '1',
        'item-text-2': '按指示預備所需物品及病床。',                    # q02 -> q05
        'item-index-2': '5',
        'item-text-3': '核對病人身份，向病人告知來意並解釋程序， 以取得病人合作。',  # q03 -> q02
        'item-index-3': '2',
        'item-text-4': '如有異常情況，必須向主責護士報告。',              # q11 -> q03
        'item-index-4': '3'
    }

    # Generate mapping
    mapping = generate_question_mapping(real_test, form_data, 'checklist')
    print(f"  Generated mapping: {mapping}")

    expected_mapping = {
        'q01': 'q01',  # No change
        'q02': 'q05',  # Changed from 2 to 5
        'q03': 'q02',  # Changed from 3 to 2
        'q11': 'q03'   # Changed from 11 to 3
    }

    # Verify mapping
    mapping_correct = True
    for orig_key, expected_new_key in expected_mapping.items():
        if mapping.get(orig_key) != expected_new_key:
            print(f"  ✗ Mapping error: {orig_key} -> {mapping.get(orig_key)}, expected {expected_new_key}")
            mapping_correct = False
        else:
            print(f"  ✓ {orig_key} -> {mapping.get(orig_key)}")

    if not mapping_correct:
        return False

    # Test staff assessment update
    print("  Testing staff assessment update with real data...")
    updated_result = {}
    for old_key, value in real_staff_result.items():
        new_key = mapping.get(old_key, old_key)
        updated_result[new_key] = value
        if old_key != new_key:
            print(f"  ✓ Updated: {old_key} -> {new_key}")

    print(f"  Original keys: {sorted(real_staff_result.keys())}")
    print(f"  Updated keys:  {sorted(updated_result.keys())}")

    # Verify the structure is preserved
    expected_updated_keys = ['q01', 'q05', 'q02', 'q03', 'basic_entry_01', 'basic_entry_02', 'additional_entry_01']
    actual_updated_keys = list(updated_result.keys())

    if set(expected_updated_keys) == set(actual_updated_keys):
        print("  ✓ Real data structure test passed!")
        return True
    else:
        print(f"  ✗ Key mismatch. Expected: {expected_updated_keys}, Got: {actual_updated_keys}")
        return False

def main():
    """Run all tests."""
    print("=== Staff Assessment Editing Test Suite ===\n")

    tests = [
        test_convert_index_to_key,
        test_generate_question_mapping,
        test_staff_assessment_update_simulation,
        test_real_data_structure
    ]

    passed = 0
    total = len(tests)

    for test_func in tests:
        if test_func():
            passed += 1

    print(f"\n=== Unit Tests Summary ===")
    print(f"Passed: {passed}/{total}")

    if passed == total:
        print("All unit tests passed! Running integration test...")
        if run_integration_test():
            print("\n🎉 All tests passed! The staff assessment editing functionality is working correctly.")
            print("✅ Implementation verified against real database structure!")
        else:
            print("\n❌ Integration test failed. Please check the database connection and permissions.")
    else:
        print(f"\n❌ {total - passed} unit test(s) failed. Please fix the issues before proceeding.")

if __name__ == "__main__":
    main()
