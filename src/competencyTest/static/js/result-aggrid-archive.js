// ARCHIVED AGGrid-related functions from result.js
// This file contains all the AGGrid functionality that was removed from result.js
// Keep this file for reference or future restoration if needed

function all__renderAGgrid(rowData) {

  const rowDataExpanded = expandRows(rowData)

  const columnDefs = [
    // {field: "Unit", minWidth: 100},
    // {field: "Name"},
    // {field: "Employee_No", minWidth: 100},
    {field: "Test", minWidth: 100},
    {
      field: "Result",
      cellRenderer: params => {
        return renderResult(params)
      },
      valueFormatter: params => {
        // Return a string representation of the object for AG Grid's internal use
        if (params.value === "[ NIL ]") {
          return "[ NIL ]";
        } else if (params.value && typeof params.value === 'object') {
          return params.value.pass ? 'PASS' : 'FAIL';
        }
        return params.value;
      },
      comparator: (valueA,valueB) => {
        return sortByPassFail(valueA,valueB)
      }
    }

  ];

  const defaultColDef = {
    flex: 1,
    minWidth: 150,
    sortable: true,
    filter: true,
  };

  const gridOptions = {
    columnDefs: columnDefs,
    defaultColDef: defaultColDef,
    rowData: rowDataExpanded,
    pagination: true,
    paginationPageSize: 50,
  };

  const gridDiv = document.querySelector('#grid');

  // Clear any existing grid content
  gridDiv.innerHTML = '';

  agGrid.createGrid(gridDiv, gridOptions);
};

function expandRows(rowData) {

  const rowDataExpanded = []

  // console.log(`Length of rowData: ${rowData.length}`)

  for (const row of rowData) {
    // console.log(`Length of result: ${row['Result'].length}`)
    // check if it is a list
    if (row['Result'] == '[ NIL ]') {
      rowDataExpanded.push({
        Test: '[ NIL ]',
        Result: '[ NIL ]'
      })
    } else {
      for (const result of row['Result']) {
        rowDataExpanded.push({
          Test: result['test_name'],
          Result: result
        })
      }
    }
  }

  return rowDataExpanded

}

function mc__renderAGgrid(rowData, testForm) {

  const columnDefs = [
    {field: "Unit", minWidth: 100},
    {field: "Name"},
    {field: "Employee_No", minWidth: 100},
    {
      field: "Result",
      cellRenderer: params => {
        return renderResult(params)
      },
      valueFormatter: params => {
        // Return a string representation of the object for AG Grid's internal use
        if (params.value === "[ NIL ]") {
          return "[ NIL ]";
        } else if (params.value && typeof params.value === 'object') {
          return params.value.pass ? 'PASS' : 'FAIL';
        }
        return params.value;
      },
      comparator: (valueA,valueB) => {
        return sortByPassFail(valueA,valueB)
      }
    }

  ];

  const defaultColDef = {
    flex: 1,
    minWidth: 150,
    sortable: true,
    filter: true,
  };

  const gridOptions = {
    columnDefs: columnDefs,
    defaultColDef: defaultColDef,
    rowData: rowData,
    pagination: true,
    paginationPageSize: 50,
    onCellClicked: params => {
      mcResult__onClick(params, testForm)
    }
      
  };

  const gridDiv = document.querySelector('#grid');

  // Clear any existing grid content
  gridDiv.innerHTML = '';

  agGrid.createGrid(gridDiv, gridOptions);

};

function checklist__renderAGgrid(rowData, testForm) {

  console.log('checklist__renderAGgrid called with rowData:', rowData)

  const columnDefs = [
    {field: "Unit", minWidth: 100},
    {field: "Name"},
    {field: "Employee_No", minWidth: 100, headerName: 'Employee No.'},
    {
      field: "Result",
      cellRenderer: params => {
        return renderResult(params)
      },
      valueFormatter: params => {
        // Return a string representation of the object for AG Grid's internal use
        if (params.value === "[ NIL ]") {
          return "[ NIL ]";
        } else if (params.value && typeof params.value === 'object') {
          return params.value.pass ? 'PASS' : 'FAIL';
        }
        return params.value;
      },
      comparator: (valueA,valueB) => {
        return sortByPassFail(valueA,valueB)
      }
    }

  ];

  const defaultColDef = {
    flex: 1,
    minWidth: 150,
    sortable: true,
    filter: true,
  };

  const gridOptions = {
    columnDefs: columnDefs,
    defaultColDef: defaultColDef,
    rowData: rowData,
    pagination: true,
    paginationPageSize: 50,
    onCellClicked: params => {
      checklistResult__onClick(params, testForm)
    }

  };


  const gridDiv = document.querySelector('#grid');
  console.log('Grid container found:', gridDiv);
  console.log('Creating grid with options:', gridOptions);

  // Clear any existing grid content
  gridDiv.innerHTML = '';

  const gridApi = agGrid.createGrid(gridDiv, gridOptions);
  console.log('Grid created successfully:', gridApi);

};

function sortByPassFail(valueA, valueB) {
  if (valueA.pass && !valueB.pass) {
    return 1;
  } else if (!valueA.pass && valueB.pass) {
    return -1;
  } else {
    return 0;
  }
};

function renderResult(params) {
  if(params.value == "[ NIL ]") {
    return '[ NIL ]';
  } else {
    console.log('Rendering Pass Fail Button')

    // Extract date from time field (handle both "YYYY-MM-DD HH:MM" and "YYYY-MM-DD" formats)
    const timeValue = params.data.Result.time || '';
    const dateOnly = timeValue.includes(' ') ? timeValue.split(' ')[0] : timeValue;

    const btnText = params.data.Result.pass
    ? `<button id="ag-${params.data.Employee_No}" style="background: none; border: none; color: green;">
      <strong>Pass on ${dateOnly}</strong>
    </button>`
    : `<button id="ag-${params.data.Employee_No}" style="background: none; border: none; color: red;">
      <strong>FAIL on ${dateOnly}</strong>
    </button>`;
    return btnText;
  }
};

function mcResult__onClick(params, testForm) {

  if (params.column.getColId() === 'Result' && params.value != '[ NIL ]') {

    const formContentObj = params.data.Result.result;

    console.log(formContentObj)

    // Add replies to the form
    for (const [key, value] of Object.entries(formContentObj)) {
      const div = document.getElementById(`div-${key}`);
      if (value.ans == value.reply){
          div.innerHTML = `
            <input
              type="radio"
              id="${key}"
              value="${value.reply}"
              checked
              disabled
            >
            <label
              style="color: green;"
              for="${key}"
            ><strong>${value.reply}</strong></label>`;
        } else {
          div.innerHTML = `
          <input
            type="radio"
            id="${key}"
            value="${value.reply}"
            checked
            disabled
          >
          <label
            style="color: red;"
            for="${key}"><strong>${value.reply}<strong></label>
          <div>
            <input type="radio" id="${key}-ans" value="${value.ans}" checked disabled>
            <label style="color: #c7edec; " for="${key}-ans">${value.ans}</label>
          </div>`;
        }

    }

    document.querySelector("#mc-result-title").innerHTML = `${params.data.Name} (${params.data.Employee_No}) [${params.data.Result.score}]`;

    $('#mc-result').modal('show');
  } else if (params.column.getColId() === 'Result' && params.value == '[ NIL ]' && params.data.Employee_No != '[ NIL ]') {

    document.querySelector('#fill-in-assessment').innerHTML = `Fill in Assessment Form for <b>${params.data.Name} (${params.data.Employee_No})</b>?`;

    // Show the confirmation modal
    $('#confirm-redirect').modal('show');

    // Fill hidden form fields
    document.getElementById('redirect-employeeNo').value = params.data.Employee_No;

    // Fill in which form to redirect
    document.getElementById('redirect-test').value = testForm.info.test_name;

    // Add event to the confirm button
    document.querySelector("#confirm-rdr-btn").onclick = function () {

      console.log('Confirm redirect');

      // Submit the form
      document.querySelector("#redirect-form").submit();

    };


  }
};

function checklistResult__onClick(params, testForm) {

  if (params.column.getColId() === 'Result' && params.value != '[ NIL ]') {

    updateChecklistModal(testForm)

    const modalBody = document.getElementById('checklist-content');

    const formContentObj = params.data.Result.result;

    fillChecklist(modalBody, formContentObj);

    document.querySelector("#checklist-result-title").innerHTML = `${params.data.Name} (${params.data.Employee_No}) [${params.data.Result.pass ? 'PASS' : 'FAIL'}]`;

    countScore();

    $('#checklist-result').modal('show');

  }

  if (params.column.getColId() === 'Result' && params.value == '[ NIL ]' && params.data.Employee_No != '[ NIL ]') {

    document.querySelector('#fill-in-assessment').innerHTML = `Fill in Assessment Form for <b>${params.data.Name} (${params.data.Employee_No}) </b>?`;

    // Show the confirmation modal
    $('#confirm-redirect').modal('show');

    // Fill hidden form fields
    document.getElementById('redirect-employeeNo').value = params.data.Employee_No;

    // Fill in which form to redirect
    document.getElementById('redirect-test').value = testForm.info.test_name;


    // Add event to the confirm button
    document.querySelector("#confirm-rdr-btn").onclick = function () {

      console.log('Confirm redirect');

      // Submit the form
      document.querySelector("#redirect-form").submit();

    };


  }
};

function exportTableToExcel() {
  gridOptions.api.exportDataAsCsv();
}

function switchTabs() {

  const tabs = document.querySelectorAll('.nav-link');

  tabs.forEach(tab => {
    tab.addEventListener('click', () => {
      tabs.forEach(t => t.classList.remove('active'));
      tab.classList.add('active');

      switch (tab.getAttribute('id')) {
        case 'tab-ag':
        var activeTable = document.getElementById('result-ag');
        break;
        case 'tab-tr':
        var activeTable = document.getElementById('result-tr');
        break;
      }

      const tables = document.querySelectorAll('.tab-pane');
      tables.forEach(t => t.classList.remove('show', 'active'));
      activeTable.classList.add('show','active');

    });

  });
}

function person_default_display() {
  const tab = document.getElementById('tab-tr');
  tab.dispatchEvent(new Event('click'));
}
