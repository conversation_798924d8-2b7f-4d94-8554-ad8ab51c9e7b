import {
  generateMCModalContent,
  generateChecklistModalContent,
  fillChecklistForm,
  countChecklistScore
} from './utils.js';

function getDataAttributes() {
  const container = document.getElementById('data-container');
  if (!container) return {};

  return {
    queryBy: container.dataset.queryBy,
    queryTest: container.dataset.queryTest,
    resultData: JSON.parse(container.dataset.resultData),
    testForm: JSON.parse(container.dataset.testForm),
    urlForView: container.dataset.urlForView
  };
}

// Function to initialize the logic
function initialize() {
  const { urlForView } = getDataAttributes();

  // Tab switching removed - using plain table only

  // Call listenBackBtn
  listenBackBtn(urlForView);

  // Call listenPlainTableTestBtns
  listenPlainTableTestBtns();

  // Initialize table filtering
  initializeTableFilters();

  // Initialize table sorting
  initializeTableSorting();

  // person_default_display removed - no longer needed without tabs

  // AGGrid rendering removed - now using plain table only

  modalOnHidden('mc-result');
  modalOnHidden('checklist-result');


}

function modalOnHidden(modalID) {
  const modal = document.getElementById(modalID)

  if (!modal) {
    console.log('Modal Not Found: ' + modalID);
    return;
  }

  // Find the button in the div .modal-header and add event listener to that button to blur on click
  const headerButtons = modal.querySelectorAll('.modal-header button');
  headerButtons.forEach(button => {
    button.addEventListener('click', () => {
      button.blur();
    });
  });

  // Find the button in the div .modal-footer and add event listener to that button to blur on click
  const footerButtons = modal.querySelectorAll('.modal-footer button');
  footerButtons.forEach(button => {
    button.addEventListener('click', () => {
      button.blur();
    });
  });
}

function listenBackBtn(urlForView) {

  const backBtn = document.getElementById('back-button');

  backBtn.addEventListener('click', () => {
    window.location.href = urlForView;
  });
}

function listenPlainTableTestBtns() {
  const plainTableTestBtns = document.querySelectorAll('.plain-table-test-btn');
  plainTableTestBtns.forEach(btn => {
    btn.addEventListener('click', (e) => {
      plainTableTestBtn__onClickHandler(e.currentTarget);
      // use currentTarget to get the clicked button but not other child elements
    });
  });
}

// AGGrid function removed - archived in result-aggrid-archive.js


// expandRows function removed - archived in result-aggrid-archive.js

// mc__renderAGgrid function removed - archived in result-aggrid-archive.js

// checklist__renderAGgrid function removed - archived in result-aggrid-archive.js



// sortByPassFail and renderResult functions removed - archived in result-aggrid-archive.js


// mcResult__onClick function removed - archived in result-aggrid-archive.js

// checklistResult__onClick function removed - archived in result-aggrid-archive.js



// switchTabs and person_default_display functions removed - archived in result-aggrid-archive.js

// exportTableToExcel function removed - archived in result-aggrid-archive.js


function plainTableTestBtn__onClickHandler(button) {

  const id = button.dataset.employeeNo;
  const testForm = JSON.parse(button.dataset.testForm);
  const name = button.dataset.name;
  const staffTest = JSON.parse(button.dataset.staffTest);


  // console.log(`testForm: ${testForm}`)  
  // console.log(`staffTest: ${staffTest}`)

  if (staffTest == "[ NIL ]" && id != '[ NIL ]') {

    document.querySelector('#fill-in-assessment').innerHTML = `Fill in Assessment Form for <b>${name}</b>?`;

    // Show the confirmation modal
    $('#confirm-redirect').modal('show');

    // Fill hidden form fields
    document.getElementById('redirect-employeeNo').value = id;

    // Fill in which form to redirect
    document.getElementById('redirect-test').value = testForm.info.test_name;


    // Add event to the confirm button
    document.querySelector("#confirm-rdr-btn").onclick = function () {
        console.log('Confirm redirect');

        // Submit the form
        document.querySelector("#redirect-form").submit();
    };
  } else if (staffTest != "[ NIL ]" && id != '[ NIL ]') {
    if (testForm.info.form_type == 'mc') {
      mcPlainShowForm(testForm,id,name,staffTest);
    } else if (testForm.info.form_type == 'checklist') {
      checklistPlainShowForm(name,staffTest,testForm);
    }
  }
}

function checklistPlainShowForm(name, staffTest, testForm) {

  updateChecklistModal(testForm)

  const modalBody = document.getElementById('checklist-content');

  const formContentObj = staffTest.result;

  fillChecklist(modalBody, formContentObj);

  document.querySelector("#checklist-result-title").innerHTML = `${name} - ${staffTest.test_name.replace('_', ' ')} [${staffTest.pass ? 'PASS' : 'FAIL'}]`;

  countScore();

  $('#checklist-result').modal('show');

}

function mcPlainShowForm(testForm,id,name,test) {

  updateMCModal(testForm)

  const formContentObj = test.result;

  // Add replies to the form
  for (const [key, value] of Object.entries(formContentObj)) {
    const div = document.getElementById(`div-${key}`);
    if (value.ans == value.reply) {
      div.innerHTML = `
        <input 
          type="radio" 
          id="${key}" 
          value="${value.reply}" 
          checked 
          disabled
        >
        <label 
          style="color: green;" 
          for="${key}"
        ><strong>${value.reply}</strong></label>`;

    } else {
      
      div.innerHTML = `
        <input type="radio" 
          id="${key}" 
          value="${value.reply}" 
          checked 
          disabled
        >
        <label 
          style="color: red;" 
          for="${key}"
        ><strong>${value.reply}<strong></label>
        <div>
          <input 
            type="radio" 
            id="${key}-ans" 
            value="${value.ans}" 
            checked 
            disabled
          >
            <label 
              style="color: #c7edec;" 
              for="${key}-ans"
            >${value.ans}</label>
        </div>`;
      }
    }

    document.querySelector("#mc-result-title").innerHTML = `${name} (${id}) [${test.score}]`;

    $('#mc-result').modal('show');
}

function updateMCModal(data) {
    // Set the modal title
    document.getElementById('mc-result-title').textContent = data.info['test_name'].replace(/_/g, ' ');

    // Generate the modal content using the utility function
    const modalContent = generateMCModalContent(data);

    // Inject the generated content into the modal body
    document.getElementById('mc-content').innerHTML = modalContent;
}

function updateChecklistModal(data) {
  // Set the modal title
  document.getElementById('checklist-result-title').textContent = data.info['test_name'].replace(/_/g, ' ');

  // Generate the modal content using the utility function
  const modalContent = generateChecklistModalContent(data);

  // Inject the generated content into the modal body
  document.getElementById('checklist-content').innerHTML = modalContent;
}

function countScore() {
  countChecklistScore();
}




function fillChecklist(modalBody, formContentObj) {
  fillChecklistForm(modalBody, formContentObj);
}

// Table filtering functionality
function initializeTableFilters() {
  try {
    // Check if filter elements exist
    const filterCollapse = document.getElementById('filterCollapse');
    if (!filterCollapse) {
      return;
    }

    // Populate filter dropdowns
    populateFilterOptions();

    // Add event listeners for filters
    const unitFilter = document.getElementById('unitFilter');
    const nameFilter = document.getElementById('nameFilter');
    const employeeNoFilter = document.getElementById('employeeNoFilter');
    const testFilter = document.getElementById('testFilter');
    const resultFilter = document.getElementById('resultFilter');
    const dateFromFilter = document.getElementById('dateFromFilter');
    const dateToFilter = document.getElementById('dateToFilter');
    const clearFiltersBtn = document.getElementById('clearFilters');

    if (unitFilter) unitFilter.addEventListener('change', applyFilters);
    if (nameFilter) nameFilter.addEventListener('input', applyFilters);
    if (employeeNoFilter) employeeNoFilter.addEventListener('input', applyFilters);
    if (testFilter) testFilter.addEventListener('change', applyFilters);
    if (resultFilter) resultFilter.addEventListener('change', applyFilters);
    if (dateFromFilter) dateFromFilter.addEventListener('change', applyFilters);
    if (dateToFilter) dateToFilter.addEventListener('change', applyFilters);
    if (clearFiltersBtn) clearFiltersBtn.addEventListener('click', clearAllFilters);

    // Initialize filter status
    updateFilterStatus();
  } catch (error) {
    console.error('Error initializing table filters:', error);
  }
}

function populateFilterOptions() {
  const container = document.getElementById('data-container');
  const queryTest = container ? container.dataset.queryTest : '';
  const table = document.querySelector('.table tbody');
  const rows = table.querySelectorAll('tr');

  // Get unique values for dropdowns
  const units = new Set();
  const tests = new Set();

  rows.forEach(row => {
    const cells = row.querySelectorAll('td');
    if (cells.length > 0) {
      if (queryTest !== 'ALL') {
        // For specific test queries, get units
        const unitCell = cells[0];
        if (unitCell && unitCell.textContent.trim() !== '[ NIL ]') {
          units.add(unitCell.textContent.trim());
        }
      } else {
        // For ALL queries, get test names
        const testCell = cells[0];
        if (testCell && testCell.textContent.trim() !== '[ NIL ]') {
          const testName = testCell.textContent.trim().split(' | ')[0];
          tests.add(testName);
        }
      }
    }
  });

  // Populate unit filter
  const unitFilter = document.getElementById('unitFilter');
  if (unitFilter) {
    units.forEach(unit => {
      const option = document.createElement('option');
      option.value = unit;
      option.textContent = unit;
      unitFilter.appendChild(option);
    });
  }

  // Populate test filter
  const testFilter = document.getElementById('testFilter');
  if (testFilter) {
    tests.forEach(test => {
      const option = document.createElement('option');
      option.value = test;
      option.textContent = test;
      testFilter.appendChild(option);
    });
  }
}

// Helper function to extract date from result text
function extractDateFromResult(resultText) {
  // Look for date patterns like "Pass on 2024-01-15" or "FAIL on 2024-01-15"
  const dateMatch = resultText.match(/\b(\d{4}-\d{2}-\d{2})\b/);
  return dateMatch ? dateMatch[1] : null;
}

// Helper function to check if a date is within the filter range
function isDateInRange(testDate, fromDate, toDate) {
  if (!testDate) return true; // If no test date found, don't filter out

  const testDateObj = new Date(testDate);
  const fromDateObj = fromDate ? new Date(fromDate) : null;
  const toDateObj = toDate ? new Date(toDate) : null;

  if (fromDateObj && testDateObj < fromDateObj) return false;
  if (toDateObj && testDateObj > toDateObj) return false;

  return true;
}

function applyFilters() {
  const container = document.getElementById('data-container');
  const queryTest = container ? container.dataset.queryTest : '';
  const table = document.querySelector('.table tbody');
  const rows = table.querySelectorAll('tr');

  // Get filter values
  const unitFilter = document.getElementById('unitFilter')?.value.toLowerCase() || '';
  const nameFilter = document.getElementById('nameFilter')?.value.toLowerCase() || '';
  const employeeNoFilter = document.getElementById('employeeNoFilter')?.value.toLowerCase() || '';
  const testFilter = document.getElementById('testFilter')?.value.toLowerCase() || '';
  const resultFilter = document.getElementById('resultFilter')?.value || '';
  const dateFromFilter = document.getElementById('dateFromFilter')?.value || '';
  const dateToFilter = document.getElementById('dateToFilter')?.value || '';

  let visibleCount = 0;

  rows.forEach(row => {
    const cells = row.querySelectorAll('td');
    let shouldShow = true;

    if (cells.length > 0) {
      if (queryTest !== 'ALL') {
        // For specific test queries: Unit, Name, Employee_No, Result
        const unitText = cells[0]?.textContent.toLowerCase() || '';
        const nameText = cells[1]?.textContent.toLowerCase() || '';
        const employeeNoText = cells[2]?.textContent.toLowerCase() || '';
        const resultCell = cells[3];

        // Apply filters
        if (unitFilter && !unitText.includes(unitFilter)) shouldShow = false;
        if (nameFilter && !nameText.includes(nameFilter)) shouldShow = false;
        if (employeeNoFilter && !employeeNoText.includes(employeeNoFilter)) shouldShow = false;

        // Result filter
        if (resultFilter && resultCell) {
          const resultText = resultCell.textContent;
          const hasPass = resultText.includes('Pass');
          const hasFail = resultText.includes('FAIL');

          if (resultFilter === 'PASS' && !hasPass) shouldShow = false;
          if (resultFilter === 'FAIL' && !hasFail) shouldShow = false;
        }

        // Date filter
        if ((dateFromFilter || dateToFilter) && resultCell) {
          const resultText = resultCell.textContent;
          const testDate = extractDateFromResult(resultText);

          if (!isDateInRange(testDate, dateFromFilter, dateToFilter)) {
            shouldShow = false;
          }
        }
      } else {
        // For ALL queries: Test, Result
        const testText = cells[0]?.textContent.toLowerCase() || '';
        const resultCell = cells[1];

        // Apply filters
        if (testFilter && !testText.includes(testFilter)) shouldShow = false;

        // Result filter
        if (resultFilter && resultCell) {
          const resultText = resultCell.textContent;
          const hasPass = resultText.includes('Pass');
          const hasFail = resultText.includes('FAIL');

          if (resultFilter === 'PASS' && !hasPass) shouldShow = false;
          if (resultFilter === 'FAIL' && !hasFail) shouldShow = false;
        }

        // Date filter
        if ((dateFromFilter || dateToFilter) && resultCell) {
          const resultText = resultCell.textContent;
          const testDate = extractDateFromResult(resultText);

          if (!isDateInRange(testDate, dateFromFilter, dateToFilter)) {
            shouldShow = false;
          }
        }
      }
    }

    // Show/hide row
    if (shouldShow) {
      row.style.display = '';
      visibleCount++;
    } else {
      row.style.display = 'none';
    }
  });

  // Update filter status
  updateFilterStatus(visibleCount, rows.length);
}

function clearAllFilters() {
  // Clear all filter inputs
  const unitFilter = document.getElementById('unitFilter');
  const nameFilter = document.getElementById('nameFilter');
  const employeeNoFilter = document.getElementById('employeeNoFilter');
  const testFilter = document.getElementById('testFilter');
  const resultFilter = document.getElementById('resultFilter');
  const dateFromFilter = document.getElementById('dateFromFilter');
  const dateToFilter = document.getElementById('dateToFilter');

  if (unitFilter) unitFilter.value = '';
  if (nameFilter) nameFilter.value = '';
  if (employeeNoFilter) employeeNoFilter.value = '';
  if (testFilter) testFilter.value = '';
  if (resultFilter) resultFilter.value = '';
  if (dateFromFilter) dateFromFilter.value = '';
  if (dateToFilter) dateToFilter.value = '';

  // Show all rows
  const table = document.querySelector('.table tbody');
  const rows = table.querySelectorAll('tr');
  rows.forEach(row => {
    row.style.display = '';
  });

  // Update filter status
  updateFilterStatus(rows.length, rows.length);
}

function updateFilterStatus(visibleCount, totalCount) {
  const filterStatus = document.getElementById('filterStatus');
  if (filterStatus) {
    // If no parameters provided, count the rows
    if (visibleCount === undefined || totalCount === undefined) {
      const table = document.querySelector('.table tbody');
      const rows = table.querySelectorAll('tr');
      const visibleRows = Array.from(rows).filter(row => row.style.display !== 'none');
      visibleCount = visibleRows.length;
      totalCount = rows.length;
    }

    if (visibleCount === totalCount) {
      filterStatus.textContent = `Showing all ${totalCount} records`;
    } else {
      filterStatus.textContent = `Showing ${visibleCount} of ${totalCount} records`;
    }
  }
}

// Table sorting functionality
function initializeTableSorting() {
  try {
    const sortableHeaders = document.querySelectorAll('.sortable');

    sortableHeaders.forEach(header => {
      header.addEventListener('click', () => {
        const column = parseInt(header.dataset.column);
        const currentSort = header.dataset.sort;

        // Reset all other headers
        sortableHeaders.forEach(h => {
          if (h !== header) {
            h.dataset.sort = 'none';
          }
        });

        // Toggle current header sort
        let newSort;
        if (currentSort === 'none' || currentSort === 'desc') {
          newSort = 'asc';
        } else {
          newSort = 'desc';
        }

        header.dataset.sort = newSort;
        sortTable(column, newSort);
      });
    });
  } catch (error) {
    console.error('Error initializing table sorting:', error);
  }
}

function sortTable(columnIndex, sortDirection) {
  const table = document.querySelector('.table tbody');
  const rows = Array.from(table.querySelectorAll('tr'));

  // Separate visible and hidden rows to maintain filter state
  const visibleRows = rows.filter(row => row.style.display !== 'none');
  const hiddenRows = rows.filter(row => row.style.display === 'none');

  // Sort only visible rows based on column content
  visibleRows.sort((a, b) => {
    const cellA = a.querySelectorAll('td')[columnIndex];
    const cellB = b.querySelectorAll('td')[columnIndex];

    if (!cellA || !cellB) return 0;

    let valueA = getCellSortValue(cellA);
    let valueB = getCellSortValue(cellB);

    // Handle different data types
    if (isNumeric(valueA) && isNumeric(valueB)) {
      valueA = parseFloat(valueA);
      valueB = parseFloat(valueB);
    } else {
      valueA = valueA.toLowerCase();
      valueB = valueB.toLowerCase();
    }

    let comparison = 0;
    if (valueA > valueB) {
      comparison = 1;
    } else if (valueA < valueB) {
      comparison = -1;
    }

    return sortDirection === 'asc' ? comparison : -comparison;
  });

  // Re-append sorted visible rows first, then hidden rows to maintain filter state
  visibleRows.forEach(row => table.appendChild(row));
  hiddenRows.forEach(row => table.appendChild(row));
}

function getCellSortValue(cell) {
  // For result cells, extract the most recent date or result text
  if (cell.querySelector('.plain-table-test-btn')) {
    const buttonText = cell.textContent.trim();

    // For split cells with multiple results, get the first (most recent) date
    const dateMatches = buttonText.match(/\b(\d{4}-\d{2}-\d{2})\b/g);
    if (dateMatches && dateMatches.length > 0) {
      return dateMatches[0]; // Return first (most recent) date for sorting
    }

    // If no date, sort by PASS/FAIL status priority (FAIL first, then PASS, then NIL)
    if (buttonText.includes('FAIL')) return '1_FAIL';
    if (buttonText.includes('Pass')) return '2_PASS';
    if (buttonText.includes('NIL')) return '3_NIL';
  }

  return cell.textContent.trim();
}

function isNumeric(str) {
  return !isNaN(str) && !isNaN(parseFloat(str));
}

// Test function for debugging filters
window.testFilters = function() {
  console.log('Testing filters...');
  const filterElements = {
    unitFilter: document.getElementById('unitFilter'),
    nameFilter: document.getElementById('nameFilter'),
    employeeNoFilter: document.getElementById('employeeNoFilter'),
    testFilter: document.getElementById('testFilter'),
    resultFilter: document.getElementById('resultFilter'),
    dateFromFilter: document.getElementById('dateFromFilter'),
    dateToFilter: document.getElementById('dateToFilter'),
    clearFiltersBtn: document.getElementById('clearFilters')
  };

  console.log('Filter elements:', filterElements);

  const table = document.querySelector('.table tbody');
  const rows = table ? table.querySelectorAll('tr') : [];
  console.log('Table rows found:', rows.length);

  return {
    filterElements,
    rowCount: rows.length,
    initializeTableFilters,
    applyFilters,
    clearAllFilters,
    initializeTableSorting,
    sortTable
  };
};

document.addEventListener('DOMContentLoaded', () => {
  try {
    initialize()
  } catch (error) {
    console.error('Error initializing result page:', error);
  }
})