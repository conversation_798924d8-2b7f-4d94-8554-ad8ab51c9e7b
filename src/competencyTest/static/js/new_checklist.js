// Convert user index like "3.1" or "4.1.1" to database key like "q03_1" or "q04_1_1"
function convertIndexToKey(userIndex) {
  if (!userIndex || userIndex.trim() === '') {
    return '';
  }

  // Remove trailing dot if present: "1." → "1"
  let cleaned = userIndex.trim().replace(/\.$/, '');

  // Split by dots: "4.1.1" → ["4", "1", "1"]
  let parts = cleaned.split('.');

  // Convert first part to zero-padded: "4" → "04"
  let mainPart = parts[0].padStart(2, '0');

  // Join remaining parts with underscores: ["1", "1"] → "_1_1"
  let subParts = parts.slice(1).join('_');

  // Combine: "q04_1_1"
  return 'q' + mainPart + (subParts ? '_' + subParts : '');
}

// Validate user index format
function validateIndex(userIndex) {
  if (!userIndex || userIndex.trim() === '') {
    return false;
  }
  // Allow formats like: "1.", "1", "3.1", "4.1.1"
  const pattern = /^\d+(\.\d+)*\.?$/;
  return pattern.test(userIndex.trim());
}

function delColumn(button) {
  const table = document.getElementById("checklist-content");
  const th = button.parentNode;

  // Find the actual column index by counting from the first deletable column
  // The first 3 columns (Index, Item, Type) are not deletable
  const headerRow = th.parentNode; // This is the <tr> containing the delete buttons
  const allThs = Array.from(headerRow.children);
  const thIndex = allThs.indexOf(th);

  // Calculate the actual column index considering the colspan="3" in the first row
  // The first <th> spans 3 columns, so deletable columns start from index 3
  const actualColumnIndex = thIndex;

  // Validate that we're trying to delete a valid column (not the first 3)
  if (actualColumnIndex < 3) {
    console.error("Cannot delete fixed columns (Index, Item, Type)");
    return;
  }

  // Loop through each row of the table and delete the cell at the determined column index
  for (let i = 0; i < table.rows.length; i++) {
    const row = table.rows[i];

    // Skip if the row doesn't have enough cells
    if (row.cells.length > actualColumnIndex) {
      row.deleteCell(actualColumnIndex);
    }
  }
}

function addColumn() {
  const table = document.getElementById("checklist-content");
  const existingColumns = table.tHead.rows[1].querySelectorAll("input.ans-col");
  const ansType = document.getElementById("new_column").value;

  let lastCount = 0;

  for (let i = 0; i < existingColumns.length; i++) {
    const columnName = existingColumns[i].getAttribute("name");
    const count = parseInt(columnName.split("-")[1]);

    if (count > lastCount) {
      lastCount = count;
    }
  }

  lastCount += 1;

  const newColumnHeader1 = document.createElement("th");
  const newColumnHeader2 = document.createElement("th");

  const button1 = document.createElement("button");
  button1.type = "button";
  button1.className = "btn btn-danger";
  button1.onclick = function() { delColumn(this); };

  const icon1 = document.createElement("i");
  icon1.className = "fa-solid fa-trash";
  icon1.style.color = "#ffffff";

  button1.appendChild(icon1);
  newColumnHeader1.appendChild(button1);
  newColumnHeader1.className = "h-center";

  const input1 = document.createElement("input");
  input1.type = "hidden";
  input1.name = `ans-${lastCount}-type`;
  input1.id = `ans-${lastCount}-type`;
  input1.value = ansType;
  

  const input2 = document.createElement("input");
  input2.type = "text";
  input2.className = "form-control ans-col";
  input2.name = `ans-${lastCount}`;
  input2.id = `ans-${lastCount}`;
  
  newColumnHeader2.appendChild(input1);
  
    // Add Count Score Checkbox
    if (ansType === "checkbox") {
      const csDiv = document.createElement("div");
      csDiv.className = "d-flex align-items-center mb-1";
      
      const csCount = document.createElement("input");
      csCount.type = "checkbox";
      csCount.id = `cs-${lastCount}`;
      csCount.name = `cs-${lastCount}`;
  
      const csLabel = document.createElement("label");
      csLabel.htmlFor = `cs-${lastCount}`;
      csLabel.className = "form-check-label ms-2";
      csLabel.textContent = "Count Score";
  
      csDiv.append(csCount, csLabel);

      newColumnHeader2.appendChild(csDiv);
  
    }

  newColumnHeader2.appendChild(input2);

  table.tHead.rows[0].appendChild(newColumnHeader1);
  table.tHead.rows[1].appendChild(newColumnHeader2);

  const rows = table.tBodies[0].rows;
  for (let i = 0; i < rows.length; i++) {
    const newRowCell = document.createElement("td");
    if (ansType === "checkbox") {
      const cb = document.createElement("input");
      cb.type = "checkbox";
      cb.className = "form-check-input";
      newRowCell.className = "h-center";
      newRowCell.appendChild(cb);
    }
    rows[i].appendChild(newRowCell);
  }

}

function delItem(button) {
  const table = button.closest("table");
  const tbodyRows = table.tBodies[0].rows.length;
  if (tbodyRows > 1) {
    const row = button.closest("tr");
    row.remove();
  }
}

function addItem() {

  // Get the table
  const table = document.getElementById("checklist-content");

  // Get the last row of the table
  const tbodyRows = table.tBodies[0].rows.length;
  const lastRow = table.tBodies[0].rows[tbodyRows - 1];

  // Create a new row based on the structure of the last row
  const newRow = document.createElement("tr");
  newRow.innerHTML = lastRow.innerHTML;

	// Clear any input values in the new row
	const inputs = newRow.querySelectorAll("input");
	inputs.forEach(function(input) {
		if (input.type === "text") {
			input.value = "";
		} else if (input.type === "checkbox") {
			input.checked = false;
		}
	});

  // Clear textarea
  const textarea = newRow.querySelector("textarea");
  if (textarea) {
		textarea.value = "";
  }

  // Clear select
  const select = newRow.querySelector("select.item-type");
  if (select) {
    select.selectedIndex = 0; // Reset to first option (Question)
  }

  const newIndex = parseInt(textarea.id.split("-")[1]) + 1;

  // Update textarea
  textarea.id = `item-${newIndex}`;
  textarea.name = `item-${newIndex}`;

  // Update index input
  const indexInput = newRow.querySelector("input.item-index");
  indexInput.id = `item-index-${newIndex}`;
  indexInput.name = `item-index-${newIndex}`;
  indexInput.value = "";
  indexInput.placeholder = `${newIndex}.`;

  // Update type select
  const typeSelect = newRow.querySelector("select.item-type");
  typeSelect.id = `item-type-${newIndex}`;
  typeSelect.name = `item-type-${newIndex}`;

	// Update essential toggle
	const essentialToggle = newRow.querySelector("input.item-essential");
	essentialToggle.id = `item-essential-${newIndex}`;
	essentialToggle.name = `item-essential-${newIndex}`;
	essentialToggle.checked = false;

	const label = newRow.querySelector("label.item-essential");
	label.setAttribute('for', `item-essential-${newIndex}`);

	essentialToggle.dispatchEvent(new Event('change'));


  // Append the new row to the table body
  table.tBodies[0].appendChild(newRow);
};

function addEntry(button) {

  // Get the div
  const card = button.closest("div.form-group");

  const newEntryDiv = `new-${card.id}-div`;
  const entryIdx = (document.querySelectorAll(`.${newEntryDiv}`).length + 1).toString().padStart(2, '0');

	const container = document.createElement('div');
	container.className = 'd-flex align-items-center mb-3';
  const div = document.createElement('div');
  div.className = `input-group ${newEntryDiv}`;

  const input = document.createElement('input');
  input.type = 'text';
  input.className = `form-control new-${card.id}-input`;
  input.id = `new-${card.id}-input-${entryIdx}`;
  input.name = `new-${card.id}-input-${entryIdx}`;
  input.placeholder = 'Entry Name';
  input.setAttribute('aria-label', "Entry Name");

  const select = document.createElement('select');
  select.className = `form-select new-${card.id}-select`;
  select.setAttribute('aria-label', 'Type of Input');
  select.id = `new-${card.id}-select-${entryIdx}`;
  select.name = `new-${card.id}-select-${entryIdx}`;
  select.setAttribute('onchange', 'entrySelect__onChange(this)');

  const selectOptions = [
      { value: "please_choose", text: "--- Choose Input Type ---" },
      { value: "test_date", text: "Test Date" },
      { value: "date", text: "Date" },
      { value: "free_text", text: "Free Text" },
      { value: "rank", text: "Rank" },
      { value: "checkbox", text: "Checkbox" },
      { value: "number", text: "Number" },
      { value: "custom_options", text: "Custom Options" }
  ]
  selectOptions.forEach(option => {
      const optionElement = document.createElement('option');
      optionElement.value = option.value;
      optionElement.text = option.text;
      select.appendChild(optionElement);
  });

  // add a checkbox labeled 'Mandatory'
  const checkbox = document.createElement('input');
  checkbox.type = 'checkbox';
  checkbox.id = `new-${card.id}-required-${entryIdx}`;
  checkbox.name = `new-${card.id}-required-${entryIdx}`;
  checkbox.className = 'form-check-input ms-2';
  checkbox.setAttribute('aria-label', 'Mandatory');
  checkbox.checked = true;

  const checkboxLabel = document.createElement('label');
  checkboxLabel.className = 'form-check-label ms-2';
  checkboxLabel.setAttribute('for', `new-${card.id}-required-${entryIdx}`);
  checkboxLabel.textContent = 'Mandatory';

  div.appendChild(input);
  div.appendChild(select);
  div.appendChild(checkbox);
  div.appendChild(checkboxLabel);

	// Create the button element
	const delBtn = document.createElement('button');
	delBtn.type = 'button';
	delBtn.className = 'btn btn-danger ms-2';
	delBtn.setAttribute('onclick', 'delEntry(this)');

	// Create the <i> element for the trash icon
	const icon = document.createElement('i');
	icon.className = 'fa-solid fa-trash';
	icon.style.color = '#ffffff';

	// Append the <i> element to the button
	delBtn.appendChild(icon);

	container.appendChild(div);
	container.appendChild(delBtn);

  // Insert the new div before the button
  card.insertBefore(container, button);
};

function entrySelect__onChange(select) {
  if (select.value === "custom_options") {
    const optionInputId = `${select.id}-custom-options`;
    const optionInput = document.createElement('input');
    optionInput.type = 'text';
    optionInput.className = 'form-control';
    optionInput.name = optionInputId;
    optionInput.placeholder = 'Use "|" to separate options';
    optionInput.id = optionInputId;
    optionInput.setAttribute('aria-label', "Custom Options");
    select.after(optionInput);
  } else {
    const optionInput = document.getElementById(`${select.id}-custom-options`);
    if (optionInput) {
      optionInput.remove();
    }
  }
}

function essential__onChange(checkbox) {
	const label = checkbox.nextElementSibling;
	if (checkbox.checked) {
		label.textContent = "Essential";
		label.className = "btn btn-danger item-essential";
	} else {
		label.textContent = "Normal";
		label.className = "btn btn-secondary item-essential";
	}

}


function delEntry(button) {
	button.closest("div.d-flex").remove();
}

// Validate all indices before form submission
function validateAllIndices() {
  const indexInputs = document.querySelectorAll('input.item-index');
  const indices = [];
  const errors = [];

  indexInputs.forEach((input, i) => {
    const value = input.value.trim();
    const rowNum = i + 1;

    if (!value) {
      errors.push(`Row ${rowNum}: Index is required`);
      return;
    }

    if (!validateIndex(value)) {
      errors.push(`Row ${rowNum}: Invalid index format "${value}". Use formats like "1.", "3.1", or "4.1.1"`);
      return;
    }

    const dbKey = convertIndexToKey(value);
    if (indices.includes(dbKey)) {
      errors.push(`Row ${rowNum}: Duplicate index "${value}" (converts to "${dbKey}")`);
      return;
    }

    indices.push(dbKey);
  });

  return errors;
}

function saveForm() {
  // Validate indices first
  const errors = validateAllIndices();

  if (errors.length > 0) {
    alert('Please fix the following errors:\n\n' + errors.join('\n'));
    return;
  }

  const form = document.getElementById("new-checklist");
  form.submit();
}

