#!/usr/bin/env python3
"""
Test script to verify the new date-based assessment structure is working correctly.
This script tests the changes made to append assessment results by date instead of overwriting.
"""

import json
from datetime import datetime
import pytz

def test_date_structure():
    """Test the new date-based assessment structure"""
    
    # Simulate the new structure
    hong_kong_tz = pytz.timezone('Asia/Hong_Kong')
    test_date_1 = datetime.now(hong_kong_tz).strftime("%Y-%m-%d")
    test_date_2 = "2025-01-15"  # Different date for testing
    
    # Test data structure
    new_structure = {
        "Assessments": {
            "TEST_NAME": {
                "v001": {
                    test_date_1: {
                        "pass": True,
                        "result": {"q01": {"o01": "true"}, "q02": {"o01": "false"}},
                        "time": test_date_1
                    },
                    test_date_2: {
                        "pass": False,
                        "result": {"q01": {"o01": "false"}, "q02": {"o01": "true"}},
                        "time": test_date_2
                    }
                }
            }
        }
    }
    
    print("=== Testing New Date-Based Assessment Structure ===")
    print(f"Test Date 1: {test_date_1}")
    print(f"Test Date 2: {test_date_2}")
    print()
    
    # Test accessing the structure
    test_name = "TEST_NAME"
    version = "v001"
    
    version_data = new_structure["Assessments"][test_name][version]
    print(f"Version data keys: {list(version_data.keys())}")
    
    # Test detecting new structure (date keys)
    is_new_structure = any(key for key in version_data.keys() if '-' in str(key) and len(str(key)) == 10)
    print(f"Is new date-based structure: {is_new_structure}")
    
    if is_new_structure:
        print("\n=== Date-based entries ===")
        for date_key, assessment in version_data.items():
            print(f"Date: {date_key}")
            print(f"  Pass: {assessment.get('pass')}")
            print(f"  Time: {assessment.get('time')}")
            print(f"  Result keys: {list(assessment.get('result', {}).keys())}")
        
        # Get latest assessment
        latest_date = max(version_data.keys())
        latest_assessment = version_data[latest_date]
        print(f"\nLatest assessment date: {latest_date}")
        print(f"Latest assessment pass: {latest_assessment.get('pass')}")
    
    print("\n=== Structure JSON ===")
    print(json.dumps(new_structure, indent=2))
    
    return True

def test_old_structure_compatibility():
    """Test that the code can handle old structure during transition"""
    
    # Old structure
    old_structure = {
        "Assessments": {
            "TEST_NAME": {
                "v001": {
                    "pass": True,
                    "result": {"q01": {"o01": "true"}},
                    "time": "2025-01-10"
                }
            }
        }
    }
    
    print("\n=== Testing Old Structure Compatibility ===")
    
    test_name = "TEST_NAME"
    version = "v001"
    
    version_data = old_structure["Assessments"][test_name][version]
    print(f"Version data keys: {list(version_data.keys())}")
    
    # Test detecting old structure (no date keys)
    is_new_structure = any(key for key in version_data.keys() if '-' in str(key) and len(str(key)) == 10)
    print(f"Is new date-based structure: {is_new_structure}")
    
    if not is_new_structure:
        print("Using old structure format")
        print(f"Pass: {version_data.get('pass')}")
        print(f"Time: {version_data.get('time')}")
        print(f"Result keys: {list(version_data.get('result', {}).keys())}")
    
    return True

if __name__ == "__main__":
    print("Testing Date-Based Assessment Structure Changes")
    print("=" * 50)
    
    try:
        test_date_structure()
        test_old_structure_compatibility()
        print("\n✅ All tests passed!")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
