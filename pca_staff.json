{"Assessments": {"PCA_002": {"v001": {"pass": true, "result": {"additional_entry_01": "滿意", "additional_entry_02": "", "additional_entry_03": "<PERSON>", "additional_entry_04": "WM / ANC", "additional_entry_05": "2025-06-23", "basic_entry_01": "<PERSON>", "basic_entry_02": "WM / ANC", "basic_entry_03": "2025-06-23", "basic_entry_04": "<PERSON>", "basic_entry_05": "WM / ANC", "basic_entry_06": "2025-06-23", "q01": {"o01": "true"}, "q02": {"o01": "true"}, "q03": {"o01": "true"}, "q04": {"o01": "true"}, "q05": {"o01": "true"}, "q06": {"o01": "true"}, "q07": {"o01": "true"}, "q08": {"o01": "true"}, "q09": {"o01": "true"}, "q10": {"o01": "true"}, "q11": {"o01": "true"}}, "time": "2025-06-23"}}, "PCA_003": {"v001": {"pass": true, "result": {"additional_entry_01": "滿意", "additional_entry_02": "", "additional_entry_03": "<PERSON>", "additional_entry_04": "WM / ANC", "additional_entry_05": "2025-06-23", "basic_entry_01": "<PERSON>", "basic_entry_02": "WM / ANC", "basic_entry_03": "2025-06-23", "basic_entry_04": "<PERSON>", "basic_entry_05": "WM / ANC", "basic_entry_06": "2025-06-23", "q01": {"o01": "true"}, "q02": {"o01": "true"}, "q03": {"o01": "true"}, "q04": {"o01": "true"}, "q05": {"o01": "true"}, "q06": {"o01": "true"}, "q07": {"o01": "true"}, "q08": {"o01": "true"}, "q09": {"o01": "true"}, "q10": {"o01": "true"}}, "time": "2025-06-23"}}, "PCA_007": {"v001": {"pass": true, "result": {"additional_entry_01": "滿意", "additional_entry_02": "", "additional_entry_03": "<PERSON>", "additional_entry_04": "APN / NO", "additional_entry_05": "2025-06-23", "basic_entry_01": "<PERSON>", "basic_entry_02": "APN / NO", "basic_entry_03": "2025-06-23", "basic_entry_04": "<PERSON>", "basic_entry_05": "APN / NO", "basic_entry_06": "2025-06-23", "q01": {"o01": "true"}, "q02": {"o01": "true"}, "q03": {"o01": "true"}, "q04": {"o01": "true"}, "q05": {"o01": "true"}, "q06": {"o01": "true"}, "q07": {"o01": "true"}, "q08": {"o01": "true"}, "q09": {"o01": "true"}, "q10": {"o01": "true"}, "q11": {"o01": "true"}, "q12": {"o01": "true"}, "q13": {"o01": "true"}}, "time": "2025-06-23"}}, "PCA_009": {"v001": {"pass": true, "result": {"additional_entry_01": "滿意", "additional_entry_02": "", "additional_entry_03": "<PERSON>", "additional_entry_04": "APN / NO", "additional_entry_05": "2025-06-23", "basic_entry_01": "<PERSON>", "basic_entry_02": "APN / NO", "basic_entry_03": "2025-06-23", "basic_entry_04": "<PERSON>", "basic_entry_05": "APN / NO", "basic_entry_06": "2025-06-23", "q01": {"o01": "true"}, "q02": {"o01": "true"}, "q03": {"o01": "true"}, "q04": {"o01": "true"}, "q05": {"o01": "true"}, "q06": {"o01": "true"}, "q07": {"o01": "true"}, "q08": {"o01": "true"}, "q09": {"o01": "true"}, "q10": {"o01": "true"}, "q11": {"o01": "true"}, "q12": {"o01": "true"}, "q13": {"o01": "true"}, "q14": {"o01": "true"}, "q15": {"o01": "true"}, "q16": {"o01": "true"}, "q17": {"o01": "true"}}, "time": "2025-06-23"}}, "PCA_010": {"v001": {"pass": true, "result": {"additional_entry_01": "滿意", "additional_entry_02": "", "additional_entry_03": "<PERSON>", "additional_entry_04": "APN / NO", "additional_entry_05": "2025-06-23", "basic_entry_01": "<PERSON>", "basic_entry_02": "APN / NO", "basic_entry_03": "2025-06-23", "basic_entry_04": "<PERSON>", "basic_entry_05": "APN / NO", "basic_entry_06": "2025-06-23", "q01": {"o01": "true"}, "q02": {"o01": "true"}, "q03": {"o01": "true"}, "q04": {"o01": "true"}, "q05": {"o01": "true"}, "q06": {"o01": "true"}, "q07": {"o01": "true"}, "q08": {"o01": "true"}, "q09": {"o01": "true"}, "q10": {"o01": "true"}, "q11": {"o01": "true"}, "q12": {"o01": "true"}, "q13": {"o01": "true"}, "q14": {"o01": "true"}, "q15": {"o01": "true"}, "q16": {"o01": "true"}, "q17": {"o01": "true"}}, "time": "2025-06-23"}}, "PCA_017": {"v001": {"pass": true, "result": {"additional_entry_01": "滿意", "additional_entry_02": "", "additional_entry_03": "<PERSON>", "additional_entry_04": "WM / ANC", "additional_entry_05": "2025-06-23", "basic_entry_01": "<PERSON>", "basic_entry_02": "WM / ANC", "basic_entry_03": "2025-06-23", "basic_entry_04": "<PERSON>", "basic_entry_05": "WM / ANC", "basic_entry_06": "2025-06-23", "q01": {"o01": "true"}, "q02": {"o01": "true"}, "q03": {"o01": "true"}, "q04": {"o01": "true"}, "q05": {"o01": "true"}, "q06": {"o01": "true"}}, "time": "2025-06-23"}}, "PCA_022": {"v001": {"pass": true, "result": {"additional_entry_01": "滿意", "additional_entry_02": "", "additional_entry_03": "<PERSON>", "additional_entry_04": "WM / ANC", "additional_entry_05": "2025-06-23", "basic_entry_01": "<PERSON>", "basic_entry_02": "WM / ANC", "basic_entry_03": "2025-06-23", "basic_entry_04": "<PERSON>", "basic_entry_05": "WM / ANC", "basic_entry_06": "2025-06-23", "q01": {"o01": "true"}, "q02": {"o01": "true"}, "q03": {"o01": "true"}, "q04": {"o01": "true"}, "q05": {"o01": "true"}, "q06": {"o01": "true"}, "q07": {"o01": "true"}, "q08": {"o01": "true"}}, "time": "2025-06-23"}}, "PCA_024": {"v001": {"pass": true, "result": {"additional_entry_01": "滿意", "additional_entry_02": "", "additional_entry_03": "<PERSON>", "additional_entry_04": "RN", "additional_entry_05": "2025-06-24", "basic_entry_01": "<PERSON>", "basic_entry_02": "RN", "basic_entry_03": "2025-06-24", "basic_entry_04": "<PERSON>", "basic_entry_05": "RN", "basic_entry_06": "2025-06-24", "q01": {"o01": "true"}, "q02": {"o01": "true"}, "q03": {"o01": "true"}, "q04": {"o01": "true"}, "q05": {"o01": "true"}, "q06": {"o01": "true"}, "q07": {"o01": "true"}, "q08": {"o01": "true"}, "q09": {"o01": "true"}, "q10": {"o01": "true"}}, "time": "2025-06-24"}}, "PCA_027": {"v001": {"pass": true, "result": {"additional_entry_01": "滿意", "additional_entry_02": "", "additional_entry_03": "<PERSON>", "additional_entry_04": "APN / NO", "additional_entry_05": "2025-06-23", "basic_entry_01": "<PERSON>", "basic_entry_02": "APN / NO", "basic_entry_03": "2025-06-23", "basic_entry_04": "<PERSON>", "basic_entry_05": "APN / NO", "basic_entry_06": "2025-06-23", "q01": {"o01": "true"}, "q02": {"o01": "true"}, "q03": {"o01": "true"}, "q04": {"o01": "true"}, "q05": {"o01": "true"}, "q06": {"o01": "true"}, "q07": {"o01": "true"}, "q08": {"o01": "true"}, "q09": {"o01": "true"}, "q10": {"o01": "true"}, "q11": {"o01": "true"}, "q12": {"o01": "true"}, "q13": {"o01": "true"}, "q14": {"o01": "true"}}, "time": "2025-06-23"}}, "PCA_028": {"v001": {"pass": true, "result": {"additional_entry_01": "滿意", "additional_entry_02": "", "additional_entry_03": "<PERSON>", "additional_entry_04": "APN / NO", "additional_entry_05": "2025-06-23", "basic_entry_01": "<PERSON>", "basic_entry_02": "APN / NO", "basic_entry_03": "2025-06-23", "basic_entry_04": "<PERSON>", "basic_entry_05": "APN / NO", "basic_entry_06": "2025-06-23", "q01": {"o01": "true"}, "q02": {"o01": "true"}, "q03": {"o01": "true"}, "q04": {"o01": "true"}, "q05": {"o01": "true"}, "q06": {"o01": "true"}, "q07": {"o01": "true"}, "q08": {"o01": "true"}, "q09": {"o01": "true"}, "q10": {"o01": "true"}, "q11": {"o01": "true"}, "q12": {"o01": "true"}, "q13": {"o01": "true"}, "q14": {"o01": "true"}, "q15": {"o01": "true"}, "q16": {"o01": "true"}, "q17": {"o01": "true"}, "q18": {"o01": "true"}, "q19": {"o01": "true"}, "q20": {"o01": "true"}, "q21": {"o01": "true"}, "q22": {"o01": "true"}, "q23": {"o01": "true"}, "q24": {"o01": "true"}, "q25": {"o01": "true"}}, "time": "2025-06-23"}}, "PCA_029": {"v001": {"pass": true, "result": {"additional_entry_01": "滿意", "additional_entry_02": "", "additional_entry_03": "<PERSON>", "additional_entry_04": "APN / NO", "additional_entry_05": "2025-06-24", "basic_entry_01": "<PERSON>", "basic_entry_02": "APN / NO", "basic_entry_03": "2025-06-24", "basic_entry_04": "<PERSON>", "basic_entry_05": "APN / NO", "basic_entry_06": "2025-06-24", "q01": {"o01": "true"}, "q02": {"o01": "true"}, "q03": {"o01": "true"}, "q04": {"o01": "true"}, "q05": {"o01": "true"}, "q06": {"o01": "true"}, "q07": {"o01": "true"}, "q08": {"o01": "true"}, "q09": {"o01": "true"}, "q10": {"o01": "true"}, "q11": {"o01": "true"}, "q12": {"o01": "true"}, "q13": {"o01": "true"}}, "time": "2025-06-24"}}, "PCA_030": {"v001": {"pass": true, "result": {"additional_entry_01": "滿意", "additional_entry_02": "", "additional_entry_03": "<PERSON>", "additional_entry_04": "RN", "additional_entry_05": "2025-06-24", "basic_entry_01": "<PERSON>", "basic_entry_02": "RN", "basic_entry_03": "2025-06-24", "basic_entry_04": "<PERSON>", "basic_entry_05": "RN", "basic_entry_06": "2025-06-24", "q01": {"o01": "true"}, "q02": {"o01": "true"}, "q03": {"o01": "true"}, "q04": {"o01": "true"}, "q05": {"o01": "true"}, "q06": {"o01": "true"}, "q07": {"o01": "true"}, "q08": {"o01": "true"}, "q09": {"o01": "true"}}, "time": "2025-06-24"}}, "PCA_Fall_Test": {"v001": {"pass": true, "result": {"q01": {"ans": "提前出院", "reply": "提前出院", "score": 1}, "q02": {"ans": "(1), (3) 及 (5)", "reply": "(1), (3) 及 (5)", "score": 1}, "q03": {"ans": "床邊", "reply": "床邊", "score": 1}, "q04": {"ans": "(1), (2) 及 (5)", "reply": "(1), (2) 及 (5)", "score": 1}, "q05": {"ans": "提醒病人｢小心地滑｣，並觀察病人自理能力，如有疑問，先向護士報告", "reply": "提醒病人｢小心地滑｣，並觀察病人自理能力，如有疑問，先向護士報告", "score": 1}, "q06": {"ans": "協助病人回病床休息，拉上床欄，給予護士召喚鈴及報告當值護士", "reply": "協助病人回病床休息，拉上床欄，給予護士召喚鈴及報告當值護士", "score": 1}, "q07": {"ans": "建議及協助病人返回病床，在護士許可下，協助病人在床邊如廁", "reply": "建議及協助病人返回病床，在護士許可下，協助病人在床邊如廁", "score": 1}, "q08": {"ans": "(1) 及 (4)", "reply": "(1) 及 (4)", "score": 1}, "q09": {"ans": "換片車放在行人通道上，方便病人拿取", "reply": "換片車放在行人通道上，方便病人拿取", "score": 1}, "q10": {"ans": "立即阻止病人，勸喻他返回床上，及通知當值護士", "reply": "立即阻止病人，勸喻他返回床上，及通知當值護士", "score": 1}}, "score": "10/10", "time": "2025-06-24 11:32", "version": "v001"}}}, "Department": "A&OT", "Employee_No": "299560", "Name": "<PERSON><PERSON>", "Unit": "IDSS"}