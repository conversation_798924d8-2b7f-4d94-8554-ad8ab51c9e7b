{"v001": {"content": {"additional_entry": {"details": {"additional_entry_01": {"input": ["滿意", "未達水平，建議覆核"], "required": true, "title": "總評分等級"}, "additional_entry_02": {"input": "free_text", "title": "評核員評語"}, "additional_entry_03": {"input": "free_text", "required": true, "title": "評核員姓名"}, "additional_entry_04": {"input": "rank", "required": true, "title": "職級"}, "additional_entry_05": {"input": "test_date", "required": true, "title": "日期"}}, "header": "評分結果"}, "basic_entry": {"details": {"basic_entry_01": {"input": "free_text", "required": true, "title": "指導員姓名"}, "basic_entry_02": {"input": "rank", "required": true, "title": "指導員職級"}, "basic_entry_03": {"input": "date", "required": true, "title": "指導講解日期"}, "basic_entry_04": {"input": "free_text", "required": true, "title": "觀察員姓名"}, "basic_entry_05": {"input": "rank", "required": true, "title": "觀察員職級"}, "basic_entry_06": {"input": "date", "required": true, "title": "觀察下實習日期"}}, "header": ""}, "intro": {"details": "", "header": "接收病人入院 (可配合 #003 使用)"}, "questions": {"details": {"items": {"q01": {"essential": true, "text": "預備需用物品前、接觸病人前後，均要清潔雙手。"}, "q02": {"essential": false, "text": "按指示預備所需物品及病床。"}, "q03": {"essential": true, "text": "核對病人身份，向病人告知來意並解釋程序， 以取得病人合作。"}, "q04": {"essential": false, "text": "帶領病人及其家屬到指定地方或病床辦理入院手續。"}, "q05": {"essential": false, "text": "向病人及其家屬介紹病房環境、設備及常規活動。"}, "q06": {"essential": true, "text": "按需要替病人量度生命表徵、身高及體重。"}, "q07": {"essential": false, "text": "指導病人留小便樣本作化驗。"}, "q08": {"essential": false, "text": "指引或協助病人更換衣服。"}, "q09": {"essential": true, "text": "檢查病人皮膚狀況。"}, "q10": {"essential": true, "text": "完成程序後，清潔雙手。"}, "q11": {"essential": true, "text": "如有異常情況，必須向主責護士報告。"}}, "options": {"o01": {"count_score": true, "name": "滿意", "type": "checkbox"}, "o02": {"count_score": false, "name": "未達水平", "type": "checkbox"}, "o03": {"count_score": false, "name": "不適用", "type": "checkbox"}, "o04": {"name": "備註", "type": "text"}}}, "header": "請在適合位置加入 ✓ 號，合格準則為所有項目的80%達滿意程度，其中有 * 之項目必須達滿意程度。", "itemcol": "項目"}}, "info": {"form_type": "checklist", "option_setting": "one", "passing_criteria": "criteria_01", "test_name": "PCA_002", "version": "v001", "version_name": "TKO-NSD-PCA002_202501"}}}