<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Validation Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .is-invalid {
            border-color: #dc3545;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
        }
        
        .test-section {
            margin: 2rem 0;
            padding: 2rem;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .validation-result {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 4px;
        }
        
        .validation-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .validation-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>Form Validation Test</h1>
        
        <!-- Test Form 1: Basic Required Fields -->
        <div class="test-section">
            <h3>Test 1: Basic Required Fields</h3>
            <form id="test-form-1">
                <div class="mb-3">
                    <label for="name1" class="form-label">Name (Required)</label>
                    <input type="text" class="form-control" id="name1" name="name1" required>
                </div>
                
                <div class="mb-3">
                    <label for="email1" class="form-label">Email (Required)</label>
                    <input type="email" class="form-control" id="email1" name="email1" required>
                </div>
                
                <div class="mb-3">
                    <label for="department1" class="form-label">Department (Required)</label>
                    <select class="form-select" id="department1" name="department1" required>
                        <option value="">Select Department</option>
                        <option value="IT">IT</option>
                        <option value="HR">HR</option>
                        <option value="Finance">Finance</option>
                    </select>
                </div>
                
                <div class="mb-3">
                    <label for="employeeno1" class="form-label">Employee Number (Required)</label>
                    <input type="number" class="form-control" id="employeeno1" name="employeeno1" required>
                </div>
                
                <button type="button" class="btn btn-primary" onclick="testValidation('test-form-1')">Test Validation</button>
                <div id="result-1" class="validation-result" style="display: none;"></div>
            </form>
        </div>
        
        <!-- Test Form 2: PCA-style Form -->
        <div class="test-section">
            <h3>Test 2: PCA-style Form</h3>
            <form id="test-form-2">
                <div class="mb-3">
                    <label for="tutor_name" class="form-label">Tutor Name (Required)</label>
                    <input type="text" class="form-control org-names" id="tutor_name" name="tutor_name" required>
                </div>
                
                <div class="mb-3">
                    <label for="tutor_date" class="form-label">Tutor Date (Required)</label>
                    <input type="date" class="form-control org-dates" id="tutor_date" name="tutor_date" required>
                </div>
                
                <div class="mb-3">
                    <label for="observer_name" class="form-label">Observer Name (Required)</label>
                    <input type="text" class="form-control org-names" id="observer_name" name="observer_name" required>
                </div>
                
                <div class="mb-3">
                    <label for="pca_name" class="form-label">PCA Name (Required)</label>
                    <input type="text" class="form-control" id="pca_name" name="pca_name" required>
                </div>
                
                <div class="mb-3">
                    <label for="pca_employeeno" class="form-label">PCA Employee No (Required)</label>
                    <input type="number" class="form-control" id="pca_employeeno" name="pca_employeeno" required>
                </div>
                
                <button type="button" class="btn btn-primary" onclick="testValidation('test-form-2')">Test Validation</button>
                <div id="result-2" class="validation-result" style="display: none;"></div>
            </form>
        </div>
        
        <!-- Test Form 3: Custom Validation Rules -->
        <div class="test-section">
            <h3>Test 3: Custom Validation Rules</h3>
            <form id="test-form-3">
                <div class="mb-3">
                    <label for="min_length_field" class="form-label">Min Length Field (5 chars min)</label>
                    <input type="text" class="form-control" id="min_length_field" name="min_length_field" 
                           data-min-length="5" required>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">Checkbox Group (Select at least one)</label>
                    <div>
                        <div class="form-check">
                            <input class="form-check-input test-option" type="checkbox" value="option1" 
                                   id="checkbox1" data-checkbox-group="test-group">
                            <label class="form-check-label" for="checkbox1">Option 1</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input test-option" type="checkbox" value="option2" 
                                   id="checkbox2" data-checkbox-group="test-group">
                            <label class="form-check-label" for="checkbox2">Option 2</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input test-option" type="checkbox" value="option3" 
                                   id="checkbox3" data-checkbox-group="test-group">
                            <label class="form-check-label" for="checkbox3">Option 3</label>
                        </div>
                    </div>
                </div>
                
                <button type="button" class="btn btn-primary" onclick="testValidation('test-form-3')">Test Validation</button>
                <div id="result-3" class="validation-result" style="display: none;"></div>
            </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script type="module">
        // Import the validation function
        import { validateFormWithScrollAndFocus } from './src/competencyTest/static/js/utils.js';
        
        // Make the function available globally for testing
        window.validateFormWithScrollAndFocus = validateFormWithScrollAndFocus;
        
        // Test function
        window.testValidation = function(formId) {
            const form = document.getElementById(formId);
            const resultDiv = document.getElementById('result-' + formId.split('-')[2]);
            
            // Clear previous results
            resultDiv.style.display = 'none';
            
            // Run validation
            const result = validateFormWithScrollAndFocus(form, {
                scrollToError: true,
                focusOnError: true,
                scrollOffset: 100,
                errorClass: 'is-invalid',
                showCustomMessages: true
            });
            
            // Show results
            resultDiv.style.display = 'block';
            if (result.isValid) {
                resultDiv.className = 'validation-result validation-success';
                resultDiv.innerHTML = '<strong>✓ Validation Passed!</strong> All fields are valid.';
            } else {
                resultDiv.className = 'validation-result validation-error';
                resultDiv.innerHTML = `<strong>✗ Validation Failed!</strong> First invalid field: ${result.firstInvalidField ? result.firstInvalidField.name || result.firstInvalidField.id : 'Unknown'}`;
            }
        };
    </script>
</body>
</html>
